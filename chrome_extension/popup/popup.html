<!-- popup/popup.html -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="popup.css">
</head>
<body>

    <!-- Subscription Status Section - Ribbon Style -->
    <span id="statusText">Unsubscribed</span>
    <div id="status-ribbon"></div>

    <!-- Display trial message only once -->
    <div id="trial-message">
        <!-- This container will display the countdown or activation prompt -->
    </div>

    <!-- Settings Section (Hidden by default) -->
    <div class="settings-section">
        <label for="timerMinutes">Entertainment time (mins):</label>
        <input type="number" id="timerMinutes" min="1" placeholder="Enter entertainment time">

        <label for="blockingMinutes">Blocking time (mins):</label>
        <input type="number" id="blockingMinutes" min="1" placeholder="Enter blocking time">

        <label for="eyeProtectionInterval">Eye protection interval (mins):</label>
        <input type="number" id="eyeProtectionInterval" min="1" placeholder="Enter eye protection interval">

        <!-- Group Premium Features -->
        <div id="premium-features-section" class="premium-section"> <!-- Added class for styling -->
            <h4 class="premium-heading">PREMIUM FEATURES</h4> <!-- Added Heading -->

            <!-- Email History Feature -->
            <div class="premium-feature-item">
                <div class="checkbox-container">
                    <input type="checkbox" id="emailHistoryEnabled">
                    <label for="emailHistoryEnabled">Email daily browsing history</label>
                </div>
                <div class="time-container">
                    <input type="time" id="emailHistoryTime" value="17:00" step="1800">
                    <span id="amPmDisplay">PM</span>
                </div>
            </div>

            <!-- Whitelist Feature -->
            <div class="premium-feature-item">
                <div class="checkbox-container">
                    <input type="checkbox" id="customWhitelistEnabled">
                    <label for="customWhitelistEnabled">Maintain your own whitelist</label>
                </div>
            </div>
        </div> <!-- End Premium Features Section -->

        <!-- Whitelist Dialog (Keep outside the premium section wrapper for positioning) -->
        <div id="whitelistDialog" class="dialog-overlay">
            <div class="dialog-content">
                <h2>Manage Your Whitelist</h2>

                <div class="input-section">
                    <input type="text" id="websiteInput" placeholder="Enter website URL (e.g., example.com)">
                    <button id="addWebsite">Add</button>
                </div>

                <div class="whitelist-entries">
                    <!-- Whitelist entries will be added here dynamically -->
                </div>

                <div class="button-container">
                    <button id="cancelButton">Cancel</button>
                    <button id="saveButton">Save</button>
                </div>
            </div>
        </div>

        <div class="buttonContainer">
            <button id="saveSettings">Save Settings</button>
        </div>
    </div>

    <!-- Add email input and activate button -->
    <div id="authentication-section">
        <input type="email" id="userEmail" placeholder="Enter your email">
        <p id="authMessage" style="color: red; margin: 5px 0; text-align: center;"></p>
        <button id="activateButton">Activate</button>
        <p class="help-text">Need help? <a href="#" id="contactSupportLink" title="Click to copy support email address">Contact Support</a></p>
    </div>

    <!-- Placeholder for subscribe prompt -->
    <div id="subscribe-prompt"></div>

    <footer>
        <p>Read our <a href="../privacy.html" target="_blank">Privacy Policy</a>.</p>
    </footer>

    <!-- Custom Modal for Confirmation Messages -->
    <div id="confirmationModal" class="modal">
        <div class="modal-content">
            <p id="confirmationMessage"></p>
        </div>
    </div>

    <!-- Include popup.js as a module -->
    <script src="popup.js"></script>
</body>
</html>
