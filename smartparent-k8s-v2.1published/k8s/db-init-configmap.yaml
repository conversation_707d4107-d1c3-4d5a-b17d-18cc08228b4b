apiVersion: v1
kind: ConfigMap
metadata:
  name: db-init-configmap
  namespace: smartparent
data:
  db-init.sql: |
    CREATE TABLE IF NOT EXISTS users (
        email TEXT PRIMARY KEY,
        subscribed BOOLEAN NOT NULL,
        plan TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS installations (
        ip_hash TEXT PRIMARY KEY,
        status TEXT NOT NULL,
        install_timestamp TIMESTAMP DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_installations_ip_hash ON installations(ip_hash);
