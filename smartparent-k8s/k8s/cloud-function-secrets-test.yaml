apiVersion: v1
kind: Secret
metadata:
  name: cloud-function-secrets # Renamed from cloud-function-secrets-test
  namespace: smartparent # Changed from smartparent-test
type: Opaque
data:
  PLAN_PRICE_IDS_STANDARD: cHJpY2VfMVFRR0N4QVI3VmxVSXJFeFg3TjVJa1dE
  PLAN_PRICE_IDS_MONTHLY: cHJpY2VfMVJPWE1hQVI3VmxVSXJFeHMxUXRGMm1h  # price_1ROXMaAR7VlUIrExs1QtF2ma (test monthly)
  PLAN_PRICE_IDS_ANNUAL: cHJpY2VfMVJPWElVQVI3VmxVSXJFeGhtZnRFY1Qy   # price_1ROXIUAR7VlUIrExhmftEcT2 (test annual)
  STRIPE_SECRET_KEY: c2tfdGVzdF81MVFQVm1tQVI3VmxVSXJFeE9aWXBuSmxoVmcyU25uTWpPbE5CdUhUNnNaUU5CZHFKZDZLZ3h0UUFCNmRaYW5rZEc2M29nelkyeHJjQVl4ZzVmZVEwQkVJcDAwaHl3RVVCOWw=
  STRIPE_WEBHOOK_SECRET: d2hzZWNfdjJIaVF1dEUzbW1pYlR0OUFObUZqY2s0T3ZkQVd5aGM=
  GROK_MODEL: Z3Jvay0zLW1pbmktZmFzdA==
  GROK_API_URL: aHR0cHM6Ly9hcGkueC5haS92MS9jaGF0L2NvbXBsZXRpb25z
  GROK_API_KEY: ****************************************************************************************************************
  #DEEPSEEK_API_URL: aHR0cHM6Ly9hcGkuZGVlcHNlZWsuY29t
  #DEEPSEEK_API_KEY: ************************************************
  FROM_EMAIL: ****************************
  EMAIL_PASSWORD: dHZjc3lsbG90a215eXh4dw==
  SMTP_HOST: c210cC5nbWFpbC5jb20=
  SMTP_PORT: NDY1
  SMTP_SECURE: dHJ1ZQ==
  DATABASE_URL: ********************************************************************************************
  SERVER_URL: aHR0cHM6Ly90ZXN0LnF1Yml0cmh5dGhtLmNvbQ==
  DB_SSL: "ZmFsc2U="  # echo -n "false" | base64
