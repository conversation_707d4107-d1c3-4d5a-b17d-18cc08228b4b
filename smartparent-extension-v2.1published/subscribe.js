document.addEventListener('DOMContentLoaded', () => {
  const errorMessage = document.getElementById('errorMessage');
  const subscribeButtons = document.querySelectorAll('.subscribe-button');

  // Get the email from the URL query parameter and pre-fill the email input
  const urlParams = new URL(window.location.href).searchParams;
  const email = urlParams.get('email');
  if (email) {
    const emailInput = document.getElementById('emailInput');
    if (emailInput) {
      emailInput.value = email;
      console.log('Pre-filled email:', email);
    }
  }

  //import SERVER_URL from './constants.js';
  const SERVER_URL = 'https://smartparent.qubitrhythm.com';

  subscribeButtons.forEach(button => {
    button.addEventListener('click', async () => {
      const plan = 'standard'; // Updated from dynamic attribute to fixed value
      const email = emailInput.value.trim();

      if (!validateEmail(email)) {
        showError('Please enter a valid email address.');
        return;
      }

      try {
        const response = await fetch(`${SERVER_URL}/create-checkout-session`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email, plan })
        });

        const data = await response.json();

        if (data.url) {
          // Redirect to the Stripe Checkout page
          window.location.href = data.url;
        } else {
          showError(data.error || 'An error occurred.');
        }
      } catch (error) {
        showError('An error occurred.');
        console.error('Error:', error);
      }
    });
  });

  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
  }

  function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  }
});
