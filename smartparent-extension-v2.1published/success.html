<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Success - SmartParent</title>
    <style>
        :root {
            --primary-color: #2E5BFF;
            --success-color: #34D399;
            --secondary-color: #2c3e50;
            --text-color: #333333;
            --background-color: #f8f9fa;
            --container-bg: #ffffff;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
            --success-bg: #ECFDF5;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px;
            padding: 40px;
            background: var(--container-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--success-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            position: relative;
            animation: scaleIn 0.5s ease-out;
        }

        .success-icon::before {
            content: "✓";
            font-weight: bold;
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .success-box {
            background-color: var(--success-bg);
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid var(--success-color);
        }

        .success-box h2 {
            color: var(--success-color);
            font-size: 1.8em;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .success-box p {
            color: var(--secondary-color);
            font-size: 1.2em;
            line-height: 1.8;
            margin: 0;
        }

        .button {
            display: inline-block;
            padding: 12px 24px;
            margin-top: 20px;
            background-color: var(--success-color);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 211, 153, 0.3);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            color: #1a3ccc;
            text-decoration: underline;
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 15px;
            }

            h1 {
                font-size: 2em;
            }

            .success-box h2 {
                font-size: 1.6em;
            }

            .success-box p {
                font-size: 1.1em;
            }

            .success-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #e1e1e1;
                --background-color: #1a1a1a;
                --container-bg: #2d2d2d;
                --success-color: #059669;
                --success-bg: #064E3B;
                --secondary-color: #a0b3cc;
            }

            .success-box {
                border-color: var(--success-color);
            }

            .success-box p {
                color: #e1e1e1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon"></div>
        <h1>Success!</h1>
        <div class="success-box">
            <h2>Welcome to SmartParent</h2>
            <p>Your account has been successfully set up, and you now have full access to all SmartParent features.</p>
        </div>
        <p>You can now continue setting up parental controls and monitoring features for a safer browsing experience.</p>
        <a href="#" class="button" onclick="window.close()">Get Started</a>
    </div>
</body>
</html>
