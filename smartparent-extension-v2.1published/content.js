// Example of getting a URL to an extension resource
const warningPageUrl = chrome.runtime.getURL('warning.html');

let warningOverlay = null;
let timerId = null;
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('[Content Script] Received message:', message); // Added log

    if (message.action === 'showWarning') {
        showWarningOverlay(message.reason);
    } else if (message.action === 'redirectWarning') {
        window.location.href = chrome.runtime.getURL('warning.html');
    } else if (message.action === 'startTimer' && message.duration) {
        showTimerNotification(message.duration);
    } else if (message.action === 'showEyeReminder') {
        showEyeReminder();
    }
});

function showWarningOverlay(reason) {
    if (warningOverlay) {
        document.body.removeChild(warningOverlay);
    }

    warningOverlay = document.createElement('div');
    warningOverlay.className = 'safety-warning-overlay';
    // Overlay styles
    warningOverlay.style.position = 'fixed';
    warningOverlay.style.top = '0';
    warningOverlay.style.left = '0';
    warningOverlay.style.width = '100%';
    warningOverlay.style.height = '100%';
    warningOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    warningOverlay.style.display = 'flex';
    warningOverlay.style.justifyContent = 'center';
    warningOverlay.style.alignItems = 'center';
    warningOverlay.style.zIndex = '10000';

    // Content container
    const contentContainer = document.createElement('div');
    contentContainer.style.backgroundColor = '#fff';
    contentContainer.style.padding = '30px';
    contentContainer.style.borderRadius = '10px';
    contentContainer.style.textAlign = 'center';
    contentContainer.style.maxWidth = '600px';
    contentContainer.style.width = '80%';
    contentContainer.style.boxShadow = '0 4px 16px rgba(0,0,0,0.3)';

    // Labels
    const titleLabel = document.createElement('p');
    titleLabel.textContent = 'This webpage may need to be monitored for minors.';
    titleLabel.style.fontSize = '1.5em';
    titleLabel.style.color = '#333';
    titleLabel.style.marginBottom = '20px';

    const reasonLabel = document.createElement('p');
    reasonLabel.textContent = `Reason: ${reason}`;
    reasonLabel.style.fontSize = '1.2em';
    reasonLabel.style.color = '#555';
    reasonLabel.style.marginBottom = '30px';

    // Buttons container
    const buttonsContainer = document.createElement('div');
    buttonsContainer.style.display = 'flex';
    buttonsContainer.style.justifyContent = 'space-between';

    // Leave button
    const leaveButton = document.createElement('button');
    leaveButton.id = 'leaveButton';
    leaveButton.textContent = 'Leave this page';
    leaveButton.style.padding = '10px 20px';
    leaveButton.style.backgroundColor = '#d9534f';
    leaveButton.style.color = '#fff';
    leaveButton.style.border = 'none';
    leaveButton.style.borderRadius = '5px';
    leaveButton.style.cursor = 'pointer';
    leaveButton.style.flex = '1';
    leaveButton.style.marginRight = '10px';

    // Continue button
    const continueButton = document.createElement('button');
    continueButton.id = 'continueButton';
    continueButton.textContent = 'Continue browsing';
    continueButton.style.padding = '10px 20px';
    continueButton.style.backgroundColor = '#5cb85c';
    continueButton.style.color = '#fff';
    continueButton.style.border = 'none';
    continueButton.style.borderRadius = '5px';
    continueButton.style.cursor = 'pointer';
    continueButton.style.flex = '1';
    continueButton.style.marginLeft = '10px';

    // Append buttons to container
    buttonsContainer.appendChild(leaveButton);
    buttonsContainer.appendChild(continueButton);

    // Append labels and buttons to content container
    contentContainer.appendChild(titleLabel);
    contentContainer.appendChild(reasonLabel);
    contentContainer.appendChild(buttonsContainer);

    // Append content to overlay
    warningOverlay.appendChild(contentContainer);

    // Append overlay to body
    document.body.appendChild(warningOverlay);

    // Event listeners
    leaveButton.addEventListener('click', () => {
        window.location.href = 'about:blank';
    });

    continueButton.addEventListener('click', () => {
        document.body.removeChild(warningOverlay);
    });
}

function showAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '50%';
    alertDiv.style.left = '50%';
    alertDiv.style.transform = 'translate(-50%, -50%)';
    alertDiv.style.backgroundColor = 'red';
    alertDiv.style.color = 'white';
    alertDiv.style.padding = '20px';
    alertDiv.style.zIndex = '10000';
    alertDiv.style.textAlign = 'center';
    alertDiv.style.borderRadius = '10px';
    alertDiv.innerText = message;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        document.body.removeChild(alertDiv);
    }, 5000); // Display the alert for 5 seconds
}

function showTimerNotification(duration) {
    // Remove existing notification if any
    const existingNotification = document.getElementById('safety-timer-notification');
    if (existingNotification) {
        document.body.removeChild(existingNotification);
    }

    // Create notification banner with fixed size and enhanced color palette
    const notification = document.createElement('div');
    notification.id = 'safety-timer-notification';
    notification.style.position = 'fixed';
    notification.style.top = '20px'; // Initial top position
    notification.style.left = '20px'; // Initial left position
    notification.style.width = '220px'; // Fixed width
    notification.style.height = '50px'; // Fixed height to accommodate text
    notification.style.backgroundColor = '#ff6666'; // Softer red for better aesthetics
    notification.style.color = '#ffffff'; // White font for contrast
    notification.style.border = '2px solid #cc0000'; // Darker border for emphasis
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 16px rgba(0,0,0,0.3)';
    notification.style.padding = '10px 20px';
    notification.style.zIndex = '10000';
    notification.style.fontSize = '1em'; // Adjusted font size
    notification.style.fontWeight = 'bold';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.cursor = 'move'; // Indicate that the timer is draggable
    notification.style.userSelect = 'none'; // Prevent text selection during drag
    notification.style.overflow = 'hidden'; // Prevent content from stretching

    let remainingTime = duration / 1000; // in seconds
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;
    const initialTimeString = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

    notification.innerHTML = `
        <div style="flex-grow: 1;">
            <strong>Time Remaining:</strong> <span id="timer">${initialTimeString}</span>
        </div>
    `;
    document.body.appendChild(notification);

    // Make the notification draggable
    makeElementDraggable(notification);

    const timerElement = document.getElementById('timer');

    const countdownInterval = setInterval(() => {
        remainingTime--;
        const minutes = Math.floor(remainingTime / 60);
        const seconds = remainingTime % 60;
        timerElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

        if (remainingTime <= 0) {
            clearInterval(countdownInterval);
            document.body.removeChild(notification);
            // Redirect to closed.html
            window.location.href = chrome.runtime.getURL('closed.html');
            // Inform background to block the site
            chrome.runtime.sendMessage({ action: 'blockSite', url: window.location.href });
        }
    }, 1000);
}

function makeElementDraggable(elmnt) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    elmnt.onmousedown = dragMouseDown;

    function dragMouseDown(e) {
        e = e || window.event;
        e.preventDefault();
        // Get the mouse cursor position at startup:
        pos3 = e.clientX;
        pos4 = e.clientY;
        document.onmouseup = closeDragElement;
        document.onmousemove = elementDrag;
    }

    function elementDrag(e) {
        e = e || window.event;
        e.preventDefault();
        // Calculate the new cursor position:
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;
        // Set the element's new position:
        elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
        elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
    }

    function closeDragElement() {
        // Stop moving when mouse button is released:
        document.onmouseup = null;
        document.onmousemove = null;
    }
}

function showEyeReminder() {
    console.log('[Content Script] Executing showEyeReminder function.'); // Added log
    // Create the overlay
    const eyeOverlay = document.createElement('div');
    eyeOverlay.id = 'eye-reminder-overlay';
    eyeOverlay.style.position = 'fixed';
    eyeOverlay.style.top = '0';
    eyeOverlay.style.left = '0';
    eyeOverlay.style.width = '100%';
    eyeOverlay.style.height = '100%';
    eyeOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.90)'; // Increased opacity
    eyeOverlay.style.display = 'flex';
    eyeOverlay.style.flexDirection = 'column';
    eyeOverlay.style.justifyContent = 'center';
    eyeOverlay.style.alignItems = 'center';
    eyeOverlay.style.zIndex = '10001'; // Ensure it's above other overlays

    // Add alert note and improved Eye SVG with realistic blinking animation
    eyeOverlay.innerHTML = `
        <p style="color: white; font-size: 24px; margin-bottom: 20px;">Time to rest your eyes!</p>
        <div id="blinking-eye" style="width: 150px; height: 150px;">
            <svg viewBox="0 0 64 64" width="100%" height="100%">
                <!-- Realistic Blinking Eye SVG -->
                <g>
                    <!-- Eye Outline -->
                    <path d="M32 12C20 12 10.67 20.5 10.67 32S20 52 32 52s21.33-8.5 21.33-20S44 12 32 12z" 
                          stroke="#fff" stroke-width="4" fill="none"/>
                    <!-- Upper Eyelid -->
                    <path id="upper-lid" d="M10.67 32C15 22 25 18 32 18s17 4 21.33 14" 
                          stroke="#fff" stroke-width="4" fill="none">
                        <animate 
                            attributeName="d" 
                            dur="1.5s" 
                            repeatCount="indefinite" 
                            values="
                                M10.67 32C15 22 25 18 32 18s17 4 21.33 14;
                                M10.67 32C15 28 25 24 32 24s17 4 21.33 8;
                                M10.67 32C15 22 25 18 32 18s17 4 21.33 14
                            "
                        />
                    </path>
                    <!-- Lower Eyelid -->
                    <path id="lower-lid" d="M10.67 32C15 42 25 46 32 46s17-4 21.33-14" 
                          stroke="#fff" stroke-width="4" fill="none">
                        <animate 
                            attributeName="d" 
                            dur="1.5s" 
                            repeatCount="indefinite" 
                            values="
                                M10.67 32C15 42 25 46 32 46s17-4 21.33-14;
                                M10.67 32C15 36 25 40 32 40s17-4 21.33-8;
                                M10.67 32C15 42 25 46 32 46s17-4 21.33-14
                            "
                        />
                    </path>
                    <!-- Pupil -->
                    <circle cx="32" cy="32" r="6" fill="#fff">
                        <animate
                            attributeName="r"
                            values="6;6;3;6;6"
                            dur="1.5s"
                            repeatCount="indefinite"
                            keyTimes="0;0.45;0.55;1"
                        />
                    </circle>
                </g>
            </svg>
        </div>
    `;

    // Add subscription reminder if trial expired
    chrome.storage.sync.get(['trialExpired', 'subscribed'], (data) => {
        if (data.trialExpired && !data.subscribed) {
            const subscribeReminder = document.createElement('div');
            subscribeReminder.style.marginTop = '20px';
            subscribeReminder.innerHTML = `
                <p style="color: white; font-size: 18px;">Subscribe now to unlock full features!</p>
                <button id="subscribeButton" style="padding: 10px 20px; background-color: #1976d2; color: #fff; border: none; border-radius: 5px; cursor: pointer;">Subscribe Now</button>
            `;
            eyeOverlay.appendChild(subscribeReminder);

            document.getElementById('subscribeButton').addEventListener('click', () => {
                window.open(chrome.runtime.getURL('subscribe.html'), '_blank');
            });
        }
    });

    document.body.appendChild(eyeOverlay);

    // Remove the overlay after 10 seconds with fade-out effect
    setTimeout(() => {
        eyeOverlay.style.transition = 'opacity 2s';
        eyeOverlay.style.opacity = '0';
        setTimeout(() => {
            if (eyeOverlay.parentNode) {
                eyeOverlay.parentNode.removeChild(eyeOverlay);
            }
        }, 2000);
    }, 20000);
}


// Handle page load events
function handlePageLoad() {
    // Only process main frame
    if (window.self === window.top) {
        const url = window.location.href;
        
        // Skip chrome-extension and other internal URLs
        if (url.startsWith('http://') || url.startsWith('https://')) {
            console.log('Content script: Processing URL:', url);
            chrome.runtime.sendMessage({ 
                type: 'PAGE_LOADED', 
                url: url
            });
        } else {
            console.log('Content script: Skipping non-web URL:', url);
        }
    }
}

// Wait for document to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', handlePageLoad);
} else {
    handlePageLoad();
}
