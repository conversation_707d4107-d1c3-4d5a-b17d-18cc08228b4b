const SERVER_URL = 'https://smartparent.qubitrhythm.com';

document.addEventListener('DOMContentLoaded', () => {
    const statusText = document.getElementById('statusText');
    const authenticationSection = document.getElementById('authentication-section');
    const settingsSection = document.querySelector('.settings-section');
    const activateButton = document.getElementById('activateButton');
    const authMessage = document.getElementById('authMessage');
    const userEmailInput = document.getElementById('userEmail'); // Renamed for clarity
    const trialMessage = document.getElementById('trial-message'); // Get trial message element
    const subscribePrompt = document.getElementById('subscribe-prompt'); // Get subscribe prompt element

    // Status Constants (mirroring backend)
    const STATUS_TRIAL_PENDING_EMAIL = 'TrialPendingEmail';
    const STATUS_TRIAL_ACTIVE = 'TrialActive';
    const STATUS_TRIAL_EXPIRED = 'TrialExpired';
    const STATUS_SUBSCRIBED = 'Subscribed';

// Helper function to clear subscription cache
function clearSubscriptionCache(callback) {
    return new Promise((resolve) => {
        chrome.storage.sync.remove(['subscribed', 'email'], () => {
            console.log('Subscription cache cleared.');
            if (callback) callback();
            resolve();
        });
    });
}

    // Add blur event listener for email validation
    userEmailInput.addEventListener('blur', () => { // Use renamed variable
        const email = userEmailInput.value.trim(); // Use renamed variable
        if (email && !validateEmail(email)) {
            authMessage.textContent = 'Please input a valid email address';
            authMessage.style.color = 'red';
        } else {
            authMessage.textContent = '';
        }
    });
    const timerMinutesInput = document.getElementById('timerMinutes');
    const blockingMinutesInput = document.getElementById('blockingMinutes');
    const saveSettingsButton = document.getElementById('saveSettings');
    const eyeProtectionIntervalInput = document.getElementById('eyeProtectionInterval');
    const emailHistoryEnabled = document.getElementById('emailHistoryEnabled');
    const emailHistoryTime = document.getElementById('emailHistoryTime');
    const amPmDisplay = document.getElementById('amPmDisplay');
    const customWhitelistEnabled = document.getElementById('customWhitelistEnabled');
    const whitelistDialog = document.getElementById('whitelistDialog');
    const websiteInput = document.getElementById('websiteInput');
    const addWebsiteBtn = document.getElementById('addWebsite');
    const whitelistEntries = document.querySelector('.whitelist-entries');
    const saveWhitelistBtn = document.getElementById('saveButton');
    const cancelWhitelistBtn = document.getElementById('cancelButton');

    let currentWhitelist = [];

    // Function to update AM/PM display
    function updateAmPmDisplay(timeValue) {
        const hour = parseInt(timeValue.split(':')[0], 10);
        amPmDisplay.textContent = hour >= 12 ? 'PM' : 'AM';
    }

    // Add event listener for time changes
    emailHistoryTime.addEventListener('change', (e) => {
        updateAmPmDisplay(e.target.value);
    });

    // Whitelist Management Functions
    function loadWhitelist() {
        chrome.storage.sync.get(['customWhitelist'], (data) => {
            currentWhitelist = data.customWhitelist || [];
            renderWhitelist();
        });
    }

    function renderWhitelist() {
        whitelistEntries.innerHTML = '';
        currentWhitelist.forEach((url, index) => {
            const entry = document.createElement('div');
            entry.className = 'whitelist-entry';
            entry.innerHTML = `
                <span>${url}</span>
                <button class="delete-btn" data-index="${index}">Delete</button>
            `;
            whitelistEntries.appendChild(entry);
        });

        // Add delete button event listeners
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                currentWhitelist.splice(index, 1);
                renderWhitelist();
            });
        });
    }

    function addWebsite() {
        let url = websiteInput.value.trim().toLowerCase();
        
        // Basic URL validation and formatting
        if (!url) return;

        // Remove http://, https://, and www.
        url = url.replace(/^(https?:\/\/)?(www\.)?/, '');
        
        // Remove anything after the first slash
        url = url.split('/')[0];

        // Check if URL is already in the list
        if (currentWhitelist.includes(url)) {
            alert('This website is already in your whitelist.');
            return;
        }

        currentWhitelist.push(url);
        websiteInput.value = '';
        renderWhitelist();
    }

    // Add event listener for whitelist checkbox
customWhitelistEnabled.addEventListener('change', () => {
    if (customWhitelistEnabled.checked) {
        loadWhitelist();
        whitelistDialog.style.display = 'flex';
    } else {
        whitelistDialog.style.display = 'none';
        currentWhitelist = [];
        chrome.runtime.sendMessage({
            type: 'whitelistSettingsChanged',
            enabled: false
        });
    }
});

document.querySelector('label[for="customWhitelistEnabled"]').addEventListener('click', () => {
    customWhitelistEnabled.checked = !customWhitelistEnabled.checked;
    customWhitelistEnabled.dispatchEvent(new Event('change'));
});

    // Add event listeners for whitelist management
    addWebsiteBtn.addEventListener('click', addWebsite);
    
    websiteInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addWebsite();
        }
    });

    saveWhitelistBtn.addEventListener('click', () => {
        chrome.storage.sync.set({ 
            customWhitelist: currentWhitelist,
            customWhitelistEnabled: true
        }, () => {
            chrome.runtime.sendMessage({
                type: 'whitelistUpdated',
                whitelist: currentWhitelist
            });
            whitelistDialog.style.display = 'none';
        });
    });
    
    cancelWhitelistBtn.addEventListener('click', () => {
        whitelistDialog.style.display = 'none';
        loadWhitelist(); // Reload the original whitelist
        chrome.storage.sync.get(['customWhitelist'], (data) => {
            if (!data.customWhitelist || data.customWhitelist.length === 0) {
                customWhitelistEnabled.checked = false;
                chrome.storage.sync.set({ customWhitelistEnabled: false });
            }
        });
    });

    // --- UI Update Functions ---

    function showLoadingState() {
        statusText.textContent = 'Loading...';
        authenticationSection.style.display = 'none';
        settingsSection.style.display = 'none';
        trialMessage.style.display = 'none';
        subscribePrompt.style.display = 'none'; // Hide subscribe prompt
    }

    function showTrialPendingEmailState(storedEmail) {
        statusText.textContent = 'Trial - Activation Required';
        statusText.classList.add('unsubscribed'); // Visually similar to unsubscribed
        statusText.classList.remove('subscribed');
        authenticationSection.style.display = 'block';
        settingsSection.style.display = 'none';
        trialMessage.innerHTML = `
            <p>Please enter your email address and click "Activate" to start your 7-day free trial.</p>
        `;
        trialMessage.style.display = 'block';
        subscribePrompt.style.display = 'none'; // Hide subscribe prompt
        if (storedEmail) {
            userEmailInput.value = storedEmail; // Pre-fill if available
        }
        authMessage.textContent = ''; // Clear previous messages
    }

    function showTrialActiveState(statusData) { // Accept full data object
        const storedEmail = statusData.email; // Extract email
        statusText.textContent = 'Trial Active (Unsubscribed)';
        statusText.classList.add('unsubscribed'); // Keep unsubscribed style during trial
        statusText.classList.remove('subscribed');
        authenticationSection.style.display = 'none'; // Hide email input
        settingsSection.style.display = 'block'; // Show settings
        // Update trial message - add countdown element and apply class for styling
        trialMessage.innerHTML = `
            <p class="trial-info">Your 7-day free trial is active. Enjoy full features!</p>
            <p id="countdown"></p> 
        `;
        trialMessage.style.display = 'block';
        // Add subscribe message to the new prompt div
        subscribePrompt.innerHTML = `
            <p>To continue after the trial, please <a id="subscribeLinkTrial" href="#">subscribe</a>.</p>
        `;
        subscribePrompt.style.display = 'block'; // Show subscribe prompt
        const subscribeLink = document.getElementById('subscribeLinkTrial');
        if (subscribeLink) {
            subscribeLink.addEventListener('click', (e) => {
                e.preventDefault();
                const subscribeUrl = chrome.runtime.getURL(`subscribe.html?email=${encodeURIComponent(storedEmail || '')}`); // Add email if available
                chrome.tabs.create({ url: subscribeUrl });
            });
        }
        loadSettings(); // Load settings for trial user

        // Start countdown if remaining time is available
        if (statusData.remainingMs > 0) {
            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                startCountdown(countdownElement, statusData.remainingMs);
            } else {
                 console.error("Countdown element not found immediately after setting innerHTML.");
            }
        } else {
             // Optionally clear any old countdown text if remainingMs is 0 or less
             const countdownElement = document.getElementById('countdown');
             if(countdownElement) countdownElement.textContent = '';
        }


        // Handle settings lock state based on password presence (same as subscribed)
        chrome.storage.sync.get(['settingsPassword', 'settingsUnlocked'], (data) => {
            if (data.settingsPassword && !data.settingsUnlocked) {
                saveSettingsButton.textContent = 'Unlock';
                saveSettingsButton.style.backgroundColor = 'red';
                lockSettings(); // Ensure UI reflects locked state
            } else {
                saveSettingsButton.textContent = 'Save & Lock Settings';
                saveSettingsButton.style.backgroundColor = 'green';
                unlockSettings(); // Ensure UI reflects unlocked state
            }
        });
    }

    function showTrialExpiredState(storedEmail) {
        statusText.textContent = 'Trial Expired';
        statusText.classList.add('unsubscribed');
        statusText.classList.remove('subscribed');
        authenticationSection.style.display = 'block'; // Show email input again
        settingsSection.style.display = 'none';
        const subscribeUrl = chrome.runtime.getURL(`subscribe.html?email=${encodeURIComponent(storedEmail || '')}`);
        trialMessage.innerHTML = `
            <p style="color:red">
                Your 7-day trial has expired. Please
                <a href="${subscribeUrl}" target="_blank">subscribe</a>
                to continue using SmartParent.
            </p>
            <p>If you have already subscribed, enter your email and click "Activate".</p>
        `;
        trialMessage.style.display = 'block';
        subscribePrompt.style.display = 'none'; // Hide subscribe prompt
        if (storedEmail) {
            userEmailInput.value = storedEmail; // Pre-fill if available
        }
        authMessage.textContent = ''; // Clear previous messages
    }

    function showSubscribedState(storedEmail) {
        statusText.textContent = 'Subscribed';
        statusText.classList.add('subscribed');
        statusText.classList.remove('unsubscribed');
        authenticationSection.style.display = 'none';
        settingsSection.style.display = 'block';
        trialMessage.style.display = 'none'; // Hide trial message
        subscribePrompt.style.display = 'none'; // Hide subscribe prompt
        loadSettings(); // Load settings for subscribed user

        // Handle settings lock state based on password presence
        chrome.storage.sync.get(['settingsPassword', 'settingsUnlocked'], (data) => {
            if (data.settingsPassword && !data.settingsUnlocked) {
                saveSettingsButton.textContent = 'Unlock';
                saveSettingsButton.style.backgroundColor = 'red';
                lockSettings();
            } else {
                saveSettingsButton.textContent = 'Save & Lock Settings';
                saveSettingsButton.style.backgroundColor = 'green';
                unlockSettings();
            }
        });
    }

    function showErrorState(errorMsg = 'Error connecting to server.') {
        statusText.textContent = 'Error';
        statusText.classList.add('unsubscribed');
        statusText.classList.remove('subscribed');
        authenticationSection.style.display = 'none'; // Hide both sections on error
        settingsSection.style.display = 'none';
        trialMessage.innerHTML = `<p style="color:red">${errorMsg}</p>`;
        trialMessage.style.display = 'block';
        subscribePrompt.style.display = 'none'; // Hide subscribe prompt
    }

    // --- Main Logic ---

    // Fetch status from the backend on popup load
    async function checkBackendStatus() {
        showLoadingState();
        try {
            const response = await fetch(`${SERVER_URL}/status`, {
                 method: 'GET', // Changed to GET as per backend update
                 mode: 'cors',
                 headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
                // Try to get error message from response body
                let errorDetails = 'Unknown server error';
                try {
                    const errorData = await response.json();
                    errorDetails = errorData.error || JSON.stringify(errorData);
                } catch (e) {
                    errorDetails = await response.text();
                }
                throw new Error(`Server error: ${response.status} - ${errorDetails}`);
            }

            const data = await response.json();
            console.log('Backend /status response:', data);

            // Update chrome.storage based on backend status
            const storageUpdate = {
                email: data.email || null, // Store email from backend
                subscribed: data.status === STATUS_SUBSCRIBED,
                inTrial: data.status === STATUS_TRIAL_ACTIVE,
                // Determine trialExpired based on status, not just absence of others
                trialExpired: data.status === STATUS_TRIAL_EXPIRED
            };
            chrome.storage.sync.set(storageUpdate, () => {
                 console.log('Chrome storage updated:', storageUpdate);
                 // Update UI based on the fetched status
                 switch (data.status) {
                    case STATUS_TRIAL_PENDING_EMAIL:
                        showTrialPendingEmailState(data.email);
                         break;
                     case STATUS_TRIAL_ACTIVE:
                         showTrialActiveState(data); // Pass full data object
                         break;
                     case STATUS_SUBSCRIBED:
                        showSubscribedState(data.email);
                        break;
                    case STATUS_TRIAL_EXPIRED:
                        showTrialExpiredState(data.email);
                        break;
                    default: // Treat any other unknown status as expired/unsubscribed
                         console.warn("Received unexpected status from backend:", data.status);
                         showTrialExpiredState(data.email); // Default to expired state
                         break;
                 }
            });

        } catch (error) {
            console.error('Error fetching /status:', error);
            showErrorState(`Failed to get status: ${error.message}`);
             // Clear storage on error to force re-activation attempt?
             chrome.storage.sync.set({ subscribed: false, inTrial: false, trialExpired: true, email: null });
        }
    }

    // Call status check on load
    checkBackendStatus();

    // Activation button event listener (calls /activate)
    activateButton.addEventListener('click', async () => {
        const email = userEmailInput.value.trim(); // Use renamed variable

        if (!validateEmail(email)) {
            authMessage.textContent = 'Please enter a valid email address.';
            authMessage.style.color = 'red';
            return;
        }

        authMessage.textContent = 'Activating...';
        authMessage.style.color = 'black';

        try {
            const response = await fetch(`${SERVER_URL}/activate`, {
                method: 'POST',
                mode: 'cors',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email })
            });

            // Try to parse JSON regardless of status code for potential error details
            let data;
            try {
                 data = await response.json();
                 console.log('Backend /activate response:', data);
            } catch (jsonError) {
                 // If JSON parsing fails, throw a more generic error
                 console.error("Failed to parse JSON response from /activate", jsonError);
                 throw new Error(`Activation failed: Server status ${response.status}, invalid response format.`);
            }


            if (!response.ok) {
                 // Use error message from parsed JSON if available
                 const errorMsg = data.error || `Activation failed: Server status ${response.status}`;
                 throw new Error(errorMsg);
            }


            // Update storage and UI based on activation result
            const storageUpdate = {
                email: data.email || null,
                subscribed: data.status === STATUS_SUBSCRIBED,
                inTrial: data.status === STATUS_TRIAL_ACTIVE,
                trialExpired: data.status === STATUS_TRIAL_EXPIRED
            };
            chrome.storage.sync.set(storageUpdate, () => {
                console.log('Chrome storage updated after activation:', storageUpdate);
                switch (data.status) {
                    case STATUS_TRIAL_ACTIVE:
                        showTrialActiveState(data.email);
                        showConfirmationModal('Trial activated successfully!');
                        break;
                    case STATUS_SUBSCRIBED:
                        showSubscribedState(data.email);
                        showConfirmationModal('Subscription activated successfully!');
                        break;
                    case STATUS_TRIAL_EXPIRED:
                        // Backend confirmed trial expired and email not subscribed
                        showTrialExpiredState(data.email); // Update UI first
                        showConfirmationModal('Trial expired. Redirecting to subscription page...');
                        // Redirect to subscribe page with email
                        setTimeout(() => {
                            const subscribeUrl = chrome.runtime.getURL(`subscribe.html?email=${encodeURIComponent(email)}`);
                            chrome.tabs.create({ url: subscribeUrl });
                        }, 2000); // Delay redirect slightly
                        break;
                    default:
                         console.warn("Received unexpected status from /activate:", data.status);
                         showErrorState('Activation failed: Unexpected status received.');
                         break;
                }
            });

        } catch (error) {
            console.error('Error during /activate call:', error);
            authMessage.textContent = `Activation error: ${error.message}`;
            authMessage.style.color = 'red';
             // Optionally revert UI to a known state, e.g., TrialExpired
             showTrialExpiredState(email); // Revert UI, keeping entered email
        }
    });

    // Save settings event listener
    saveSettingsButton.addEventListener('click', async () => {
        if (saveSettingsButton.textContent === 'Unlock') {
            // Prompt user for password to unlock
            chrome.storage.sync.get(['settingsPassword'], (res) => {
                if (!res.settingsPassword) {
                    showErrorModal("No password is set yet. Please create one first.");
                    return;
                }
                const enteredPwd = prompt('Enter your SmartParent password:');
                if (!enteredPwd) {
                    return; // Do nothing if user clicks Cancel
                }
                if (verifyPassword(enteredPwd, res.settingsPassword)) {
                    unlockSettings();
                } else {
                    showErrorModal("Wrong password. Please find the password from your inbox with subject 'Your password of SmartParent.'");
                }
            });
            return;
        }

        const timerMinutes = parseInt(timerMinutesInput.value, 10);
        const blockingMinutes = parseInt(blockingMinutesInput.value, 10);
        const eyeProtectionInterval = parseInt(eyeProtectionIntervalInput.value, 10);

        if (isNaN(timerMinutes) || isNaN(blockingMinutes) || isNaN(eyeProtectionInterval) ||
            timerMinutes <= 0 || blockingMinutes <= 0 || eyeProtectionInterval <= 0) {
            alert('Please enter valid numbers for all settings.');
            return;
        }

        // Check if password is stored before saving settings
        chrome.storage.sync.get(['settingsPassword', 'email'], (res) => { // Keep email fetch for password sending
            const saveAndLockSettings = (pwd = null) => {
                 // Always attempt to lock settings now, regardless of trial/subscribed status
                 // const shouldLock = statusText.textContent !== 'Trial Active (Unsubscribed)'; // REMOVED

                // Save all settings including email history and whitelist
                chrome.storage.sync.set({
                    timerMinutes,
                    blockingMinutes,
                    eyeProtectionInterval,
                    emailHistoryEnabled: emailHistoryEnabled.checked,
                    emailHistoryTime: emailHistoryTime.value,
                    customWhitelistEnabled: customWhitelistEnabled.checked
                }, () => {
                    // Notify background script about settings changes
                    chrome.runtime.sendMessage({
                        type: 'emailHistorySettingsChanged',
                        enabled: emailHistoryEnabled.checked,
                        time: emailHistoryTime.value
                    });
                    chrome.runtime.sendMessage({
                        type: 'whitelistSettingsChanged',
                        enabled: customWhitelistEnabled.checked
                    });
                });

                // Always lock settings after saving
                // if (shouldLock) { // REMOVED Condition
                    // Lock settings inputs
                    lockSettings();

                    if (pwd) {
                    // If a new password was set, send it via email
                    sendPasswordEmail(pwd, res.email);
                    showConfirmationModal('Password has been sent to your email address. Settings have been saved and locked.');
                } else {
                    showConfirmationModal('Settings have been saved and locked.');
                }

                    // Change button style to reflect locked state
                    // Change button style to reflect locked state
                    saveSettingsButton.textContent = 'Unlock';
                    saveSettingsButton.style.backgroundColor = 'red';
                // } else { // REMOVED Else block for trial mode
                //      // Just saved, didn't lock (Trial mode)
                //      showConfirmationModal('Settings saved.');
                //      // Keep button as "Save Settings"
                //      saveSettingsButton.textContent = 'Save Settings';
                //      saveSettingsButton.style.backgroundColor = '#4CAF50';
                // } // REMOVED
            };

             // Always prompt for password if none exists, regardless of trial/subscribed status
             // const needsPassword = statusText.textContent !== 'Trial Active (Unsubscribed)'; // REMOVED

            if (!res.settingsPassword) { // Simplified condition: prompt if no password exists
                // Prompt user to set new password
                const pwd = prompt('Create a new password to lock these settings:');
                if (pwd) {
                    const hashed = hashPassword(pwd);
                    // Store hashed password and save settings
                    chrome.storage.sync.set({ settingsPassword: hashed }, () => {
                        saveAndLockSettings(pwd);
                    });
                }
                // If user clicks Cancel, do nothing
                return;
            } else {
                 // If password already exists OR we are in trial (don't need password yet)
                saveAndLockSettings(); // Function now handles conditional locking
            }
        });
    });

    // Attempt to unlock on input focus if locked
    [timerMinutesInput, blockingMinutesInput, eyeProtectionIntervalInput, emailHistoryTime].forEach(input => {
        input.addEventListener('focus', () => {
            chrome.storage.sync.get(['settingsPassword'], (res) => {
                if (res.settingsPassword && input.disabled) {
                    const enteredPwd = prompt('Enter your SmartParent password:');
                    if (!enteredPwd) {
                        return; // Do nothing if user clicks Cancel
                    }
                    if (verifyPassword(enteredPwd, res.settingsPassword)) {
                        unlockSettings();
                    } else {
                        showErrorModal("Wrong password. Please find the password from your inbox with subject 'Your password of SmartParent.'");
                    }
                }
            });
        });
    });

    // Add event listener for checkbox to enable/disable time input
    emailHistoryEnabled.addEventListener('change', () => {
        emailHistoryTime.disabled = !emailHistoryEnabled.checked;
    });

    function loadSettings() {
        chrome.storage.sync.get(['timerMinutes', 'blockingMinutes', 'eyeProtectionInterval', 'emailHistoryEnabled', 'emailHistoryTime', 'settingsPassword', 'settingsUnlocked', 'customWhitelistEnabled'], (data) => {
            timerMinutesInput.value = data.timerMinutes || 15;
            blockingMinutesInput.value = data.blockingMinutes || 60;
            eyeProtectionIntervalInput.value = data.eyeProtectionInterval || 30;
            emailHistoryEnabled.checked = data.emailHistoryEnabled || false;
            const timeValue = data.emailHistoryTime || '00:00';
            emailHistoryTime.value = timeValue;
            emailHistoryTime.disabled = !emailHistoryEnabled.checked;
            updateAmPmDisplay(timeValue);
            
            // Load whitelist settings
            customWhitelistEnabled.checked = data.customWhitelistEnabled || false;
            if (customWhitelistEnabled.checked) {
                loadWhitelist();
                // Don't show dialog on initial load
                currentWhitelist = data.customWhitelist || [];
            }

            // Apply locked state if password exists and not unlocked
            if (data.settingsPassword && !data.settingsUnlocked) {
                lockSettings();
            } else {
                unlockSettings();
            }
        });
    }

    // Utility function to validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(String(email).toLowerCase());
    }

    // Helper function to lock
    function lockSettings() {
        timerMinutesInput.disabled = true;
        blockingMinutesInput.disabled = true;
        eyeProtectionIntervalInput.disabled = true;
        emailHistoryTime.disabled = true;
        emailHistoryEnabled.disabled = true;
        customWhitelistEnabled.disabled = true;
        chrome.storage.sync.set({ settingsUnlocked: false });
    }

    // Helper function to unlock
    function unlockSettings() {
        timerMinutesInput.disabled = false;
        blockingMinutesInput.disabled = false;
        eyeProtectionIntervalInput.disabled = false;
        emailHistoryEnabled.disabled = false;
        emailHistoryTime.disabled = !emailHistoryEnabled.checked;
        customWhitelistEnabled.disabled = false;
        saveSettingsButton.textContent = 'Save & Lock Settings';
        saveSettingsButton.style.backgroundColor = 'green';
        chrome.storage.sync.set({ settingsUnlocked: true });
    }

    // Simple hashing (demo; replace with a real hash if needed)
    function hashPassword(password) {
        return btoa(password);
    }

    function verifyPassword(plain, hashed) {
        return btoa(plain) === hashed;
    }

    function sendPasswordEmail(password, email) {
        if (!email) return;
        fetch(`${SERVER_URL}/send-password-email`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password })
    }).catch(err => console.error('Error sending password email:', err));
}
});

// --- Countdown Timer Function ---
let countdownInterval = null; // Keep track of the interval

function startCountdown(element, remainingMs) {
    if (countdownInterval) {
        clearInterval(countdownInterval); // Clear existing interval if any
    }

    if (!element) {
        console.error("Cannot start countdown: element is null");
        return;
    }

    let remainingSeconds = Math.max(0, Math.floor(remainingMs / 1000));

    function updateCountdown() {
        if (remainingSeconds <= 0) {
            element.textContent = 'Trial expired.';
            clearInterval(countdownInterval);
            // Optionally trigger a status refresh here
            // checkBackendStatus(); 
            return;
        }

        const days = Math.floor(remainingSeconds / (24 * 60 * 60));
        const hours = Math.floor((remainingSeconds % (24 * 60 * 60)) / (60 * 60));
         const minutes = Math.floor((remainingSeconds % (60 * 60)) / 60);
         const seconds = remainingSeconds % 60;

         // Use innerHTML to apply different styles via spans
         const prefix = `<span class="countdown-prefix">Remaining days: </span>`;
         const timeValue = `<span class="countdown-time">${days} Day${days !== 1 ? 's' : ''} - ${String(hours).padStart(2, '0')}h${String(minutes).padStart(2, '0')}m${String(seconds).padStart(2, '0')}s</span>`;
         element.innerHTML = prefix + timeValue;
         
         remainingSeconds--;
    }

    updateCountdown(); // Initial display
    countdownInterval = setInterval(updateCountdown, 1000); // Update every second
}
// Removed unused checkTrialStatus function

function showConfirmationModal(message) {
    const modal = document.getElementById('confirmationModal');
    const messageElement = document.getElementById('confirmationMessage');
    
    if (!modal || !messageElement) {
        console.error('Modal elements not found in the DOM.');
        return;
    }

    messageElement.textContent = message;
    modal.classList.add('show');
    modal.style.display = 'flex';

    // Automatically hide the modal after 3 seconds
    setTimeout(() => {
        modal.classList.remove('show');
        // Allow transition to complete before hiding
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }, 3000);
}

function showErrorModal(message) {
    const modal = document.getElementById('confirmationModal');
    const messageElement = document.getElementById('confirmationMessage');
    if (!modal || !messageElement) {
        console.error('Modal elements not found in the DOM.');
        return;
    }
    messageElement.textContent = message;
    messageElement.style.color = 'red';
    modal.classList.add('show');
    modal.style.display = 'flex';
    // Automatically hide after 3 seconds
    setTimeout(() => {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            messageElement.style.color = '';
        }, 300);
    }, 3000);
}
