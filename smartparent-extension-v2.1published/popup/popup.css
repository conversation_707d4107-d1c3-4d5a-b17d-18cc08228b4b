/* Global Styles */
html, body {
    width: 300px;
    min-width: 300px;
    margin: 0;
    padding: 15px 5px 15px 5px; /* Top Right Bottom Left */
    padding-right: 15px; /* Add extra padding to right side */
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    box-sizing: border-box;
}

/* Main sections layout - centered with no extra padding */
#subscription-status, 
#authentication-section,
#trial-message,
.settings-section {
    width: 284px; /* 300px - (8px * 2) padding */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0;
    margin: 0 auto 15px;
}

/* Authentication section - remove conflicting padding */
#authentication-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 0;
    margin: 0 auto;
    box-sizing: border-box;
}

/* Email input and activate button consistent widths */
#authentication-section #userEmail,
#authentication-section #activateButton {
    width: 90%; /* Consistent width for both, matching #trial-message */
    margin: 8px auto;
    display: block;
    padding: 10px;
    box-sizing: border-box;
}

/* Reset other elements to not interfere */
input[type="number"],
.settings-section label {
    width: 90%;
    margin: 5px auto;
}

/* Welcome message style */
#trial-message {
    width: 90%;
    margin: 0 auto;
    padding: 10px 0;
    background-color: #E3F2FD; /* Light blue background */
    border-radius: 4px;
    border: 1px solid #BBDEFB; /* Slightly darker blue border */
}

#trial-message p {
    width: 95%;
    margin: 8px auto;
    /* Default paragraph style within trial message */
    font-size: 1.1em; /* Keep existing size */
    line-height: 1.4; /* Keep existing line height */
}

/* Specific style for the main trial info text */
#trial-message .trial-info {
    color: #01579B; /* Darker blue text */
}

/* Styling for the countdown container paragraph */
#trial-message #countdown {
    font-size: 1em; /* Slightly smaller than main text */
    /* Color and weight will be set by inner spans */
}

/* Style for the "Remaining days: " prefix */
#trial-message #countdown .countdown-prefix {
    color: #01579B; /* Darker blue text, same as .trial-info */
    font-weight: normal; /* Ensure prefix is not bold */
}

/* Style for the actual time value */
#trial-message #countdown .countdown-time {
    color: #D32F2F; /* Keep red color */
    font-weight: bold; /* Make time value bold */
}

/* Form elements and inputs */
input[type="email"],
input[type="number"],
button,
.settings-section label {
    width: 90%;
    margin: 5px auto;
    display: block;
    padding: 10px;
    box-sizing: border-box;
}

/* Input fields styling */
input[type="email"], /* Keep general email styling if needed elsewhere */
input[type="number"] {
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Button styling */
/* Specific styling for activate button - width handled above */
#activateButton {
    /* width: 95%; /* Removed, handled by the shared rule */
    padding: 10px;
    margin: 8px auto; /* Kept margin */
    display: block; /* Kept display */
    box-sizing: border-box; /* Kept box-sizing */
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
}

#activateButton:hover {
    background-color: #218838;
}

#saveSettings {
    width: 80%;
    padding: 10px 20px;
    font-size: 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    margin: 10px auto;
}

#saveSettings:hover {
    background-color: #45a049;
}

/* Status text styling */
#statusText {
    font-size: 1.5em;
    font-weight: bold;
    margin: 15px 0;
    text-align: center;
}

#statusText.subscribed {
    color: #28a745;
}

#statusText.unsubscribed {
    color: #dc3545;
}

#status {
    color: #28a745;
    font-weight: 500;
}

/* Settings section specific styles */
.settings-section {
    margin-top: 20px;
}

.settings-section h2 {
    width: 90%;  /* Match input width */
    margin: 0 auto 20px;  /* Center align with auto margins */
    color: #4A90E2; /* Light blue color */
    font-weight: bold;
    text-align: center; /* Center the text */
    /* padding-left: 0; Removed as text-align: center handles alignment */
}

.settings-section label {
    color: #444;
    font-size: 13px;
    font-weight: bold;
}

/* Helper styles */
.buttonContainer {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 10px 0;
    padding: 0;
}

#authMessage {
    width: 95%;
    text-align: center;
    color: red;
    margin: 8px auto;
}

.help-text {
    text-align: center;
    margin: 15px 0 10px 0;
    font-size: 13px;
    color: #666;
}

.help-text a {
    color: #007BFF;
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

/* Footer styles */
footer {
    margin-top: 20px;
    font-size: 12px;
    text-align: center;
    color: #777;
}

footer a {
    color: #007BFF;
    text-decoration: none;
}

/* Email History Section */
.email-history-section {
    width: 90%;
    margin: 15px auto;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
}

.checkbox-container input[type="checkbox"] {
    width: auto !important;
    margin: 2px;
    cursor: pointer;
}

.checkbox-container label {
    font-size: 13px;
    color: #444;
    white-space: nowrap;
}

.time-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-top: 6px;
    width: 100%;
}

.time-container input[type="time"] {
    width: 100px !important;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #666;
}

.time-container input[type="time"]::-webkit-calendar-picker-indicator {
    margin-left: 4px;
}

.time-container input[type="time"]::-webkit-datetime-edit-ampm-field {
    display: none;
}

.time-container input[type="time"]::-webkit-clear-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

#amPmDisplay {
    margin-left: 4px;
    font-size: 13px;
    color: #666;
    min-width: 26px;
}

.checkbox-container label[for="emailHistoryEnabled"]::after,
.checkbox-container label[for="customWhitelistEnabled"]::after {
    content: "";
}

.whitelist-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 15px auto;
    width: 90%;
}

/* Dialog Overlay */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog-content {
    background-color: white;
    padding: 16px;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dialog-content h2 {
    color: #555;
    font-size: 16px;
    margin: 0 0 12px 0;
    text-align: center;
    line-height: 1.2;
}

.input-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

#websiteInput {
    flex: 1;
    padding: 0 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    height: 32px;
    line-height: normal;
    box-sizing: border-box;
}

#addWebsite {
    padding: 0 12px;
    background-color: #007BFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    width: auto !important;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    margin: 0;
}

#addWebsite:hover {
    background-color: #0056b3;
}

.whitelist-entries {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
}

.whitelist-entry {
    display: flex;
    align-items: center;
    padding: 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 4px;
    font-size: 13px;
}

.whitelist-entry span {
    flex: 1;
    margin-right: 8px;
    word-break: break-all;
}

.whitelist-entry:last-child {
    margin-bottom: 0;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    width: 60px !important;
    min-width: 60px !important;
    margin: 0 !important;
    height: 24px;
    line-height: 16px;
}

.delete-btn:hover {
    background-color: #c82333;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
}

#cancelButton,
#saveButton {
    padding: 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    width: 80px !important;
    height: 32px;
    line-height: 32px;
}

#cancelButton {
    background-color: #6c757d;
    color: white;
}

#cancelButton:hover {
    background-color: #5a6268;
}

#saveButton {
    background-color: #28a745;
    color: white;
}

#saveButton:hover {
    background-color: #218838;
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-content {
    background-color: #f3f6d6;
    padding: 20px;
    font-size: small;
    color: #dc3545;
    border-radius: 5px;
    text-align: center;
    width: 80%; 
    max-width: 200px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal.show {
    opacity: 1;
}

/* Subscribe Prompt Styling */
#subscribe-prompt {
    display: none; /* Hidden by default, shown by JS */
    width: 90%; /* Match other sections */
    margin: 0 auto 10px; /* Center and add 10px bottom margin for gap above footer */
    padding: 0;
    text-align: center; /* Center the text */
}

#subscribe-prompt p {
    margin: 0; /* Remove default paragraph margin */
    font-size: 1em; /* Adjust font size if needed */
    line-height: 1.4;
}

#subscribe-prompt a {
    color: #007BFF;
    text-decoration: none;
    font-weight: bold;
}

#subscribe-prompt a:hover {
    text-decoration: underline;
}
