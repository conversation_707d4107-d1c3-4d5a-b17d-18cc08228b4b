:root {
    --primary-color: #2E5BFF;
    --primary-gradient: linear-gradient(135deg, #2E5BFF 0%, #1a3ccc 100%);
    --secondary-color: #2c3e50;
    --text-color: #333333;
    --text-light: #666666;
    --background-color: #f8f9fa;
    --container-bg: #ffffff;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
    --success-color: #34D399;
    --error-color: #FF3B30;
    --border-color: #e3e3e3;
    --hover-bg: #f0f7ff;
}

@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #e1e1e1;
        --text-light: #a0b3cc;
        --background-color: #1a1a1a;
        --container-bg: #2d2d2d;
        --border-color: #3d3d3d;
        --hover-bg: #2a2a2a;
    }
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

header {
    background: var(--primary-gradient);
    color: #fff;
    padding: 60px 0;
    text-align: center;
    box-shadow: var(--box-shadow);
    margin-bottom: 40px;
}

header h1 {
    margin: 0;
    font-size: 3em;
    font-weight: 600;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.features, .video-tutorial, .plans, .why-subscribe, .about {
    padding: 20px 20px;
    max-width: 1200px;
    margin: 0 auto 30px;
    background-color: var(--container-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-item {
    text-align: center;
    padding: 30px;
    border-radius: var(--border-radius);
    background-color: var(--container-bg);
    transition: all 0.3s ease;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(46,91,255,0.15);
    border-color: var(--primary-color);
}

.feature-icon {
    font-size: 2.5em;
    margin-bottom: 20px;
}

.feature-item h3 {
    color: var(--primary-color);
    margin: 15px 0;
    font-size: 1.3em;
    font-weight: 600;
}

.feature-item p {
    color: var(--text-light);
    font-size: 1em;
    line-height: 1.6;
}

.plan-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin: 40px auto;
    max-width: 1200px;
}

.plan {
    background-color: var(--container-bg);
    border: 2px solid var(--border-color);
    width: 100%;
    max-width: 380px;
    text-align: center;
    padding: 40px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    box-shadow: var(--box-shadow);
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 700px;
}

.plan .plan-header {
    flex-shrink: 0;
}

.plan ul {
    flex-grow: 1;
    margin: 30px 0;
    list-style: none;
    padding: 0;
}

.plan button, 
.plan input[type="email"] {
    margin-top: auto;
    width: calc(100% - 30px);
}

.plan.enterprise {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-gradient);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 8px rgba(46,91,255,0.2);
}

.plan li.included {
    color: var(--text-color);
}

.plan li.disabled {
    color: var(--text-light);
    text-decoration: line-through;
}

.plan li.disabled::before {
    content: "✗";
    color: var(--text-light);
}

.basic-button {
    width: calc(100% - 30px);
    padding: 16px 30px;
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 10px 0;
}

.basic-button:hover {
    background: var(--hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46,91,255,0.15);
}

.plan.enterprise:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(46,91,255,0.15);
}

.plan-header {
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid var(--border-color);
}

.price {
    font-size: 3.5em;
    color: var(--primary-color);
    margin: 20px 0;
    font-weight: 600;
}

.price span {
    font-size: 0.35em;
    color: var(--text-light);
    font-weight: normal;
}

.plan h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 2em;
    font-weight: 600;
}

.plan li {
    padding: 12px 0;
    color: var(--text-color);
    font-size: 1.1em;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
}

.plan li::before {
    content: "✓";
    color: var(--primary-color);
    margin-right: 10px;
    font-weight: bold;
}

.plan input[type="email"] {
    width: calc(100% - 30px);
    padding: 15px;
    margin: 20px 0;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1.1em;
    transition: all 0.3s ease;
    background-color: var(--container-bg);
    color: var(--text-color);
}

.plan input[type="email"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 4px 8px rgba(46,91,255,0.15);
}

.subscribe-button {
    width: calc(100% - 30px);
    padding: 16px 30px;
    background: var(--primary-gradient);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 12px rgba(46,91,255,0.3);
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    margin: 10px 0;
}

.subscribe-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(46,91,255,0.4);
    filter: brightness(1.1);
}

.error {
    color: var(--error-color);
    background-color: var(--container-bg);
    padding: 12px;
    border-radius: 8px;
    margin: 20px 0;
    font-size: 0.9em;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--error-color);
}

footer {
    background: var(--primary-gradient);
    color: #fff;
    padding: 40px 0;
    text-align: center;
    margin-top: 60px;
    box-shadow: 0 -4px 12px rgba(0,0,0,0.1);
}

footer p {
    margin: 0;
    opacity: 0.9;
}

footer nav {
    margin-top: 20px;
}

footer nav a {
    color: #fff;
    margin: 0 15px;
    text-decoration: none;
    font-weight: 500;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

footer nav a:hover {
    opacity: 1;
    text-decoration: underline;
}

h2 {
    color: var(--primary-color);
    text-align: center;
    font-size: 2.2em;
    margin-bottom: 40px;
    font-weight: 600;
}

.why-subscribe p, .about p {
    font-size: 1.1em;
    color: var(--text-light);
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.video-container { /* Renamed from video-placeholder */
    display: flex; /* Enable Flexbox */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically (optional, but good practice) */
    margin: 20px 0; /* Add some space around the container */
    background-color: var(--container-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px; /* Add padding inside the container */
    overflow: hidden; /* Ensure iframe doesn't overflow */
}

.video-container iframe {
    max-width: 100%; /* Make iframe responsive */
    border-radius: var(--border-radius); /* Optional: round corners */
    box-shadow: var(--box-shadow); /* Optional: add shadow to iframe */
}

@media screen and (max-width: 768px) {
    header h1 {
        font-size: 2em;
    }

    .plan-container {
        padding: 0 20px;
    }

    .plan {
        transform: scale(1) !important;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }
}
