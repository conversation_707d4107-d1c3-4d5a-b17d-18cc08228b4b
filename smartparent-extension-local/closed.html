<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <title>Time Limit Reached - SmartParent</title>
    <style>
        /* CSS Variables for consistent styling - Matched with popup.css */
        :root {
            /* Force Light Theme */
            color-scheme: light;

            /* Primary Colors */
            --primary-color: #4285f4;
            --primary-light: #5a95f5;
            --primary-dark: #3367d6;
            --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

            /* Secondary Colors */
            --secondary-color: #5e35b1;
            --secondary-light: #7e57c2;
            --secondary-dark: #4527a0;
            --secondary-gradient: linear-gradient(135deg, #5e35b1 0%, #4527a0 100%);

            /* Accent Colors */
            --accent-color: #f4b400;
            --accent-light: #ffc107;
            --accent-dark: #e69c00;
            --accent-gradient: linear-gradient(135deg, #f4b400 0%, #e69c00 100%);

            /* Status Colors */
            --success-color: #0f9d58;
            --success-light: #4caf50;
            --success-bg: rgba(15, 157, 88, 0.08);
            --error-color: #db4437;
            --error-light: #ef5350;
            --error-bg: rgba(219, 68, 55, 0.08);
            --warning-color: #f4b400;
            --info-color: #4285f4;
            --alert-color: #db4437;

            /* Neutral Colors */
            --text-color: #202124;
            --text-light: #5f6368;
            --text-lighter: #80868b;
            --border-color: #dadce0;
            --border-light: #f1f3f4;

            /* Background Colors */
            --background-color: #e8f0fe;
            --container-bg: #f8f9fa;
            --hover-bg: rgba(66, 133, 244, 0.08);
            --premium-bg: #e8f0fe;
            --dialog-bg: #fef7e0;
            --modal-bg: #e6f4ea;
            --subscribe-bg: #fce8e6;
            --alert-bg: #fce8e6;

            /* UI Elements */
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
            --button-radius: 8px;
            --input-radius: 8px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            background-image:
                url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px;
            padding: 40px;
            background-color: var(--container-bg);
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-align: center;
            border: 1px solid var(--border-light);
        }

        .time-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            position: relative;
            animation: scaleIn 0.5s ease-out;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .time-icon::before {
            content: "⏰";
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-box {
            background-color: var(--premium-bg);
            border-radius: var(--border-radius);
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            border: 1px solid rgba(66, 133, 244, 0.2);
            box-shadow: var(--box-shadow);
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%234285f4' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
        }

        .alert-box h2 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'Arial', 'Helvetica Neue', sans-serif;
            font-weight: 600;
        }

        .alert-box p {
            color: var(--text-color);
            font-size: 1.2em;
            line-height: 1.8;
            margin: 0;
        }

        .countdown-container {
            background: var(--primary-gradient);
            border-radius: var(--border-radius);
            padding: 20px;
            margin: 25px 0;
            text-align: center;
            box-shadow: var(--box-shadow);
            border: 1px solid rgba(66, 133, 244, 0.3);
            position: relative;
            overflow: hidden;
        }

        .countdown-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
        }

        .countdown-title {
            color: white;
            font-size: 1.1em;
            margin-bottom: 15px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            z-index: 1;
        }

        .countdown-display {
            color: white;
            font-size: 3em;
            font-weight: 700;
            font-family: 'Courier New', monospace;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
            animation: pulse 2s infinite;
        }

        .countdown-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9em;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .countdown-expired {
            background: var(--success-color) !important;
            background-image: linear-gradient(135deg, #0f9d58 0%, #4caf50 100%) !important;
        }

        .countdown-expired .countdown-display {
            animation: none;
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 15px;
            }

            h1 {
                font-size: 2em;
            }

            .alert-box h2 {
                font-size: 1.6em;
            }

            .alert-box p {
                font-size: 1.1em;
            }

            .time-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }

            .countdown-display {
                font-size: 2.2em;
            }

            .countdown-title {
                font-size: 1em;
            }
        }

        /* Disable dark mode completely */
        @media (prefers-color-scheme: dark) {
            /* This media query is intentionally left empty to prevent dark mode */
        }
    </style>
    <script>
        // Force light mode
        document.documentElement.setAttribute('data-color-scheme', 'light');
    </script>
</head>
<body style="background-color: #e8f0fe !important;">
    <div class="container">
        <div class="time-icon"></div>
        <h1>Time Limit Reached</h1>
        <div class="alert-box">
            <h2>Access Closed</h2>
            <p>You have reached your browsing time limit for this website. Please take a break and try again later.</p>
        </div>

        <div class="countdown-container" id="countdownContainer">
            <div class="countdown-title">Time Until Access Resumes</div>
            <div class="countdown-display" id="countdownDisplay">--:--</div>
            <div class="countdown-subtitle">Please take a break and return when the timer expires</div>
        </div>
    </div>

    <script>
        // Countdown functionality
        function initializeCountdown() {
            const countdownDisplay = document.getElementById('countdownDisplay');
            const countdownContainer = document.getElementById('countdownContainer');

            // Get the blocking period end time from local storage
            chrome.storage.local.get(['blockingPeriodEndTime'], (data) => {
                if (data.blockingPeriodEndTime) {
                    const unblockAt = new Date(data.blockingPeriodEndTime);

                    updateCountdown(countdownDisplay, countdownContainer, unblockAt);

                    // Update every second
                    const interval = setInterval(() => {
                        updateCountdown(countdownDisplay, countdownContainer, unblockAt);
                    }, 1000);

                    // Store interval ID for cleanup
                    window.countdownInterval = interval;
                } else {
                    // Fallback: get settings and assume blocked just now
                    chrome.storage.sync.get(['blockingMinutes'], (settings) => {
                        const blockingMinutes = settings.blockingMinutes || 60; // Default 60 minutes
                        const blockingDurationMs = blockingMinutes * 60 * 1000;
                        const unblockAt = new Date(Date.now() + blockingDurationMs);

                        updateCountdown(countdownDisplay, countdownContainer, unblockAt);

                        const interval = setInterval(() => {
                            updateCountdown(countdownDisplay, countdownContainer, unblockAt);
                        }, 1000);

                        window.countdownInterval = interval;
                    });
                }
            });
        }

        function updateCountdown(displayElement, containerElement, unblockTime) {
            const now = new Date();
            const timeRemaining = unblockTime.getTime() - now.getTime();

            if (timeRemaining <= 0) {
                // Time expired
                displayElement.textContent = '00:00';
                containerElement.classList.add('countdown-expired');
                displayElement.parentElement.querySelector('.countdown-title').textContent = 'Access Restored!';
                displayElement.parentElement.querySelector('.countdown-subtitle').textContent = 'You can now refresh this page to continue browsing';

                // Clear the interval
                if (window.countdownInterval) {
                    clearInterval(window.countdownInterval);
                }

                // Optionally auto-refresh after a few seconds
                setTimeout(() => {
                    window.location.reload();
                }, 3000);

                return;
            }

            // Calculate hours, minutes, and seconds
            const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
            const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

            // Format display
            if (hours > 0) {
                displayElement.textContent = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            } else {
                displayElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeCountdown);

        // Also initialize immediately in case DOMContentLoaded already fired
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeCountdown);
        } else {
            initializeCountdown();
        }
    </script>
</body>
</html>
