// Whitelist configuration
    const EDUCATIONAL_WHITELIST = [
        '*.wikipedia.org',
        '*.google.com',
        '*.apple.com',
        '*.jstor.org',
        '*.researchgate.net',
        '*.edu',
        '*.ac.uk',
        '*.edu.au',
        '*.edu.sg',
        '*.edu.hk',
        '*.khanacademy.org',
        '*.coursera.org',
        '*.edx.org',
        '*.udemy.com',
        '*.udacity.com',
        '*.ck12.org',
        '*.pbslearningmedia.org',
        '*.brainpop.com',
        '*.nationalgeographic.com',
        '*.ted.com',
        '*.britannica.com',
        '*.merriam-webster.com',
        '*.dictionary.com',
        '*.thesaurus.com',
        '*.oxfordreference.com',
        '*.wolframalpha.com',
        '*.desmos.com',
        '*.geogebra.org',
        '*.nasa.gov',
        '*.sciencemag.org',
        '*.gutenberg.org',
        '*.poetryfoundation.org',
        '*.grammarly.com',
        '*.turnitin.com',
        '*.easybib.com',
        '*.history.com',
        '*.archives.gov',
        '*.britishmuseum.org',
        '*.smithsonian.museum',
        '*.loc.gov',
        '*.apa.org',
        '*.psychologytoday.com',
        '*.verywellmind.com',
        '*.mind.org.uk',
        '*.nimh.nih.gov',
        '*.asanet.org',
        '*.sociology.org',
        '*.anthrosource.net',
        '*.culturalanthropology.org',
        '*.sapiens.org',
        '*.brookings.edu',
        '*.carnegieendowment.org',
        '*.crisisgroup.org',
        '*.chathamhouse.org',
        '*.foreignaffairs.com',
        '*.nber.org',
        '*.repec.org',
        '*.ssrn.com',
        '*.imf.org',
        '*.worldbank.org',
        '*.law.cornell.edu',
        '*.supremecourt.gov',
        '*.justia.com',
        '*.oyez.org',
        '*.findlaw.com',
        '*.historytoday.com',
        '*.gilderlehrman.org',
        '*.history.org',
        '*.britishmuseum.org',
        '*.metmuseum.org',
        '*.ed.gov',
        '*.unesco.org',
        '*.oecd.org/education',
        '*.education.gov.uk',
        '*.acer.org',
        '*.plato.stanford.edu',
        '*.iep.utm.edu',
        '*.philosophybasics.com',
        '*.ethics.org.au',
        '*.philpapers.org',
        '*.bced.gov.bc.ca',
        '*.canada.ca',
        '*.cemc.uwaterloo.ca',
        '*.edu.gov.on.ca',
        '*.education.gouv.qc.ca',
        '*.gc.ca',
        '*.learnalberta.ca',
        '*.ontario.ca',
        '*.statcan.gc.ca',
        '*.tc.gc.ca',
        '*.alberta.ca',
        '*.camosun.ca',
        '*.concordia.ca',
        '*.dal.ca',
        '*.ecolecatholique.ca',
        '*.ecolepublique.ca',
        '*.hec.ca',
        '*.mcgill.ca',
        '*.mcmaster.ca',
        '*.ocadu.ca',
        '*.ontariocolleges.ca',
        '*.queensu.ca',
        '*.ryerson.ca',
        '*.sfu.ca',
        '*.torontomu.ca',
        '*.ubc.ca',
        '*.uottawa.ca',
        '*.utoronto.ca',
        '*.uvic.ca',
        '*.waterloo.ca',
        '*.westernu.ca',
        '*.yorku.ca',
        '*.cbe.ab.ca',
        '*.epsb.ca',
        '*.hdsb.ca',
        '*.pdsb.net',
        '*.sd23.bc.ca',
        '*.sd43.bc.ca',
        '*.sd61.bc.ca',
        '*.tdsb.on.ca',
        '*.vsb.bc.ca',
        '*.wrdsb.ca',
        '*.bcit.ca',
        '*.bowvalleycollege.ca',
        '*.cna.nl.ca',
        '*.collegeboreal.ca',
        '*.conestogac.on.ca',
        '*.fanshawec.ca',
        '*.georgiancollege.ca',
        '*.humber.ca',
        '*.langara.ca',
        '*.libraries.coop',
        '*.library.yorku.ca',
        '*.mohawkcollege.ca',
        '*.nait.ca',
        '*.nlc.bc.ca',
        '*.nscc.ca',
        '*.ocls.ca',
        '*.ontariotechu.ca',
        '*.opentextbc.ca',
        '*.sait.ca',
        '*.senecacollege.ca',
        '*.sheridancollege.ca',
        '*.spl.ca',
        '*.tpl.ca',
        '*.tru.ca',
        '*.ualberta.ca',
        '*.ufv.ca',
        '*.ulaval.ca',
        '*.umanitoba.ca',
        '*.usask.ca',
        '*.vpl.ca',
        '*.wlu.ca',
        'search.brave.com'
    ];

    // User whitelist storage
    let customWhitelist = [];
    let combinedWhitelist = [...EDUCATIONAL_WHITELIST];

    // Initialize storage and load custom whitelist
    chrome.storage.sync.get(['customWhitelist', 'customWhitelistEnabled'], (result) => {
        if (result.customWhitelistEnabled && result.customWhitelist) {
            customWhitelist = result.customWhitelist;
            combinedWhitelist = [...EDUCATIONAL_WHITELIST, ...customWhitelist];
        } else {
            combinedWhitelist = [...EDUCATIONAL_WHITELIST];
        }
    });

    // Listen for storage changes
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'sync') {
            if (changes.customWhitelist || changes.customWhitelistEnabled) {
                chrome.storage.sync.get(['customWhitelist', 'customWhitelistEnabled'], (result) => {
                    if (result.customWhitelistEnabled && result.customWhitelist) {
                        customWhitelist = result.customWhitelist;
                        combinedWhitelist = [...EDUCATIONAL_WHITELIST, ...customWhitelist];
                    } else {
                        customWhitelist = [];
                        combinedWhitelist = [...EDUCATIONAL_WHITELIST];
                    }
                });
            }
        }
    });

    //import { SERVER_URL, CHECK_ENDPOINT } from './constants.js';

    // Define SERVER_URL and Endpoints
    const SERVER_URL = 'http://localhost:32410'; // Keep local URL for testing
    const CHECK_ENDPOINT = '/check';
    const STATUS_ENDPOINT = '/status'; // Added status endpoint

    // Status Constants (mirroring backend)
    const STATUS_FREE = 'Free'; // Added
    const STATUS_TRIAL_PENDING_EMAIL = 'TrialPendingEmail'; // Legacy
    const STATUS_TRIAL_ACTIVE = 'TrialActive';
    const STATUS_TRIAL_EXPIRED = 'TrialExpired'; // Legacy
    const STATUS_SUBSCRIBED = 'Subscribed';

    console.log('Background script loading...');

    // --- GA4 Measurement Protocol Helper (Client-Side) ---
    const GA_MEASUREMENT_ID = 'G-7L1FR0MPFW'; // Your Measurement ID
    const GA_ENDPOINT = 'https://www.google-analytics.com/mp/collect';

    // Function to send GA4 events via fetch
    // Note: This client-side approach doesn't use the API Secret
    async function sendGA4Event(eventName, eventParams = {}) {
      try {
        // Generate a unique client ID if one doesn't exist, store it for persistence
        let { clientId } = await chrome.storage.local.get('clientId');
        if (!clientId) {
          clientId = self.crypto.randomUUID();
          await chrome.storage.local.set({ clientId });
        }

        // Basic session tracking (simplified) - generate a new session ID if needed
        let { sessionId, sessionTimestamp } = await chrome.storage.session.get(['sessionId', 'sessionTimestamp']);
        const now = Date.now();
        // Start new session if > 30 mins have passed or no session ID
        if (!sessionId || !sessionTimestamp || (now - sessionTimestamp > 30 * 60 * 1000)) {
            sessionId = `${now}`; // Simple timestamp as session ID
            sessionTimestamp = now;
            await chrome.storage.session.set({ sessionId, sessionTimestamp });
        }

        const body = {
          client_id: clientId,
          non_personalized_ads: false, // Optional: set to true to disable ad personalization
          events: [{
            name: eventName,
            params: {
              session_id: sessionId, // Include session ID
              engagement_time_msec: "1", // Required for user engagement, can be simple value
              debug_mode: false, // Set to true for debugging in GA4 DebugView
              ...eventParams, // Spread custom event parameters
            },
          }],
        };

        // Use fetch to send the event
        const response = await fetch(`${GA_ENDPOINT}?measurement_id=${GA_MEASUREMENT_ID}`, {
          method: 'POST',
          body: JSON.stringify(body),
          headers: {
            'Content-Type': 'application/json',
          },
          // Important: Keepalive allows the request to potentially outlive the service worker
          // Still not 100% guaranteed for all scenarios, but improves reliability
          keepalive: true,
        });

        if (!response.ok) {
          console.warn(`GA4 Event "${eventName}" failed: ${response.status} ${response.statusText}`, await response.text());
        } else {
           // Optionally log success, but might be too noisy
           // console.log(`GA4 Event "${eventName}" sent successfully.`);
        }

      } catch (error) {
        console.error(`Error sending GA4 event "${eventName}":`, error);
      }
    }

    // Send the initial background script loaded event
    sendGA4Event('background_script_loaded');

    // Enhanced message listener with logging
    chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
        console.log('Background received message:', message, 'from:', sender);

        if (message.type === 'PAGE_LOADED') {
            // Skip extension pages and empty URLs
            if (!message.url || message.url.startsWith('chrome-extension://')) {
                console.log('Skipping extension URL:', message.url);
                return;
            }

            try {
                const urlObj = new URL(message.url);
                console.log('Processing URL hostname:', urlObj.hostname);

                const isWhitelisted = combinedWhitelist.some(domain => {
                    if (domain.startsWith('*')) {
                        // Handle wildcard domains like *.example.com
                        const baseDomain = domain.slice(2);
                        const matches = urlObj.hostname.endsWith(baseDomain);
                        if (matches) {
                            console.log(`Matched wildcard domain ${domain} for ${urlObj.hostname}`);
                        }
                        return matches;
                    }
                    const matches = urlObj.hostname === domain;
                    if (matches) {
                        console.log(`Matched exact domain ${domain}`);
                    }
                    return matches;
                });

                if (isWhitelisted) {
                    console.log('URL is whitelisted, scheduling eye reminder only:', message.url);
                    scheduleEyeReminder(message.url, sender.tab.id);
                    return;
                }

                // Only proceed with safety check if not whitelisted
                // Use the new userStatus field
                chrome.storage.sync.get(['userStatus', 'email'], async (data) => {
                    const status = data.userStatus;
                    const email = data.email;
                    const canCheck = status === STATUS_FREE || status === STATUS_TRIAL_ACTIVE || status === STATUS_SUBSCRIBED;

                    sendGA4Event('site_check_triggered', {
                        page_location: message.url, // Standard GA4 param for URL
                        is_whitelisted: 'false', // Params usually strings
                        user_status: status || 'unknown' // Send the actual status string
                    });

                    if (canCheck) {
                        console.log(`Processing non-whitelisted page (${status}):`, message.url);
                        // Pass email only if it exists, backend might not always need it for Free checks
                        checkPageSafety(message.url, sender.tab.id, email);
                    } else {
                        // This case should be rare now with the Free tier, but handles legacy/error states
                        console.log(`Web monitoring inactive (Status: ${status}). Skipping check.`);
                        sendGA4Event('site_check_skipped_inactive', { page_location: message.url, user_status: status || 'unknown' });
                    }
                });
            } catch (error) {
                console.error('Error processing URL:', error);
                return;
            }
        } else if (message.type === 'emailHistorySettingsChanged') {
            // Handle email history settings changes
            updateEmailHistoryAlarm(message.enabled, message.time);
        } else if (message.type === 'whitelistSettingsChanged') {
            // Handle whitelist settings changes - Check status first
            chrome.storage.sync.get(['userStatus', 'customWhitelist'], (result) => {
                const isPremiumFeatureActive = result.userStatus === STATUS_TRIAL_ACTIVE || result.userStatus === STATUS_SUBSCRIBED;
                if (isPremiumFeatureActive && message.enabled && result.customWhitelist) {
                    customWhitelist = result.customWhitelist;
                    combinedWhitelist = [...EDUCATIONAL_WHITELIST, ...customWhitelist];
                    console.log('Custom whitelist enabled and loaded.');
                } else {
                    customWhitelist = [];
                    combinedWhitelist = [...EDUCATIONAL_WHITELIST];
                    console.log('Custom whitelist disabled or user not premium/trial.');
                }
            });
        } else if (message.type === 'whitelistUpdated') {
            // Handle whitelist content updates - Check status first
            customWhitelist = message.whitelist; // Update local copy regardless
            chrome.storage.sync.get(['userStatus', 'customWhitelistEnabled'], (result) => {
                 const isPremiumFeatureActive = result.userStatus === STATUS_TRIAL_ACTIVE || result.userStatus === STATUS_SUBSCRIBED;
                if (isPremiumFeatureActive && result.customWhitelistEnabled) {
                    combinedWhitelist = [...EDUCATIONAL_WHITELIST, ...customWhitelist];
                     console.log('Custom whitelist updated and applied.');
                } else {
                    // If not premium or not enabled, ensure combined list doesn't use custom list
                    combinedWhitelist = [...EDUCATIONAL_WHITELIST];
                     console.log('Custom whitelist updated but not applied (user not premium/trial or feature disabled).');
                }
            });
        } else if (message.type === 'ERROR') { // Listen for error messages from checkPageSafety
            // Handle or display the error message if needed, e.g., show a notification
            console.error("Received error from safety check:", message.error);
            // chrome.notifications.create({ type: 'basic', iconUrl: 'icons/icon128.png', title: 'SmartParent Error', message: message.error });
        } else if (message.type === 'GA4_EVENT') { // Listen for GA4 event messages
            console.log(`Forwarding GA4 Event: ${message.eventName}`, message.eventParams);
            sendGA4Event(message.eventName, message.eventParams);
            // No need to sendResponse or return true for these fire-and-forget events
            return; // Stop further processing for this message type
        }

        return true; // Keep message channel open for async responses from other message types
    });

    // Function to update email history alarm based on user settings
    function updateEmailHistoryAlarm(enabled, time) {
        console.log('Updating email history alarm:', enabled, time);

        // Clear existing alarm
        chrome.alarms.clear('dailyHistoryEmail');

        if (!enabled) {
            console.log('Email history disabled, alarm cleared');
            return;
        }

        // Parse the time string (HH:mm) and set up new alarm
        const [hours, minutes] = time.split(':').map(Number);
        console.log(`Setting up alarm for ${hours}:${minutes}`);

        // Calculate the next occurrence of the specified time
        const now = new Date();
        let scheduledTime = new Date();
        scheduledTime.setHours(hours, minutes, 0, 0); // Set hours and minutes, with 0 seconds

        // If the scheduled time has already passed today, set it for tomorrow
        if (now > scheduledTime) {
            scheduledTime.setDate(scheduledTime.getDate() + 1);
        }

        // Create the alarm to trigger at the exact time and repeat daily
        chrome.alarms.create('dailyHistoryEmail', {
            when: scheduledTime.getTime(),
            periodInMinutes: 24 * 60 // 24 hours in minutes
        });

        console.log('Daily history email scheduled for:', scheduledTime.toLocaleString());

        // Verify the alarm was created
        chrome.alarms.get('dailyHistoryEmail', (alarm) => {
            if (alarm) {
                console.log('Alarm verified. Next fire time:', new Date(alarm.scheduledTime).toLocaleString());
            } else {
                console.error('Failed to create alarm');
            }
        });
    }

    // Function to handle page load event
    function handlePageLoaded(url) {
        chrome.storage.sync.get(['subscribed', 'email'], (data) => {
            console.log('Background script: Subscription data:', data);

            if (data.subscribed && data.email) {
                // Proceed to analyze the URL
                analyzeUrl(url, data.email);
            } else {
                console.log('User is not subscribed. Skipping analysis.');
            }
        });
    }

    // Modify the checkPageSafety function to handle fetch errors
    async function checkPageSafety(url, tabId, email) {
        console.log('Processing safety check for:', url, 'tabId:', tabId);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

        try {
            const apiUrl = `${SERVER_URL}${CHECK_ENDPOINT}`;
            console.log('Calling API:', apiUrl, 'with email:', email);

            let response;
            try {
                response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ url, email }),
                    signal: controller.signal
                });
            } finally {
                clearTimeout(timeoutId);
            }

            console.log('API response received. Status:', response.status);

            // Check if response exists and is ok
            if (!response || !response.ok) {
                const errorText = await response?.text() || 'No response received';
                console.error('Error response from server:', errorText);
                throw new Error(`Server responded with status ${response?.status || 'unknown'}`);
            }

            let data;
            try {
                data = await response.json();
                console.log('API response data:', data);
            } catch (parseError) {
                console.error('Failed to parse JSON response:', parseError);
                throw new Error('Invalid JSON response from server');
            }

            // Validate response data
            if (!data || typeof data.is_safe === 'undefined') {
                console.error('Invalid response format:', data);
                throw new Error('Invalid response format from server');
            }

            const hostname = new URL(url).hostname; // Declare hostname ONCE here, before the if/else

            if (data.is_safe === false) {
                console.log('Site marked as unsafe, redirecting to warning page');
                // Use hostname declared above
                sendGA4Event('site_blocked_unsafe', { page_location: url, hostname: hostname });
                // Redirect the current tab immediately
                chrome.tabs.update(tabId, { url: chrome.runtime.getURL('closed.html') });
                // Block the domain - Assuming blockSite exists elsewhere or removing it if not implemented
                // blockSite(hostname, tabId); // Pass tabId for immediate redirection if needed - Ensure this function exists!
            } else {
                 // Site is safe (is_safe is true), schedule eye reminder regardless of game content
                console.log('Site is safe, scheduling eye reminder.');
                scheduleEyeReminder(url, tabId);

                // Now check if it contains games to show timer/warning
                if (data.contains_games === true) {
                    console.log('Site also contains games, showing timer/warning.');
                    chrome.storage.sync.get(['timerMinutes', 'blockingMinutes'], (settings) => {
                        const timerDuration = (settings.timerMinutes || 15) * 60 * 1000;
                        const blockingDuration = (settings.blockingMinutes || 60) * 60 * 1000;
                        // Use hostname declared above - remove redundant declaration

                        sendGA4Event('site_game_timer_started', {
                            page_location: url,
                            hostname: hostname,
                            timer_duration_ms: timerDuration,
                            block_duration_ms: blockingDuration
                        });

                        chrome.tabs.sendMessage(tabId, {
                            action: 'showWarning',
                            reason: 'This site contains games or entertainment content.'
                        });
                        chrome.tabs.sendMessage(tabId, {
                            action: 'startTimer',
                            duration: timerDuration
                        });
                        // Schedule the start of the blocking period after the timer duration
                        // Use hostname declared higher up in the function scope
                        scheduleBlockingPeriodStart(hostname, timerDuration, blockingDuration);
                    });
                } else {
                    console.log('Site is safe and does not contain games or entertainment.');
                    sendGA4Event('site_safe_no_games', { page_location: url });
                    // Eye reminder already scheduled above
                }
            }
        } catch (error) {
            console.error('Safety check failed:', error);
            if (error.name === 'AbortError') {
                console.error('Request timed out after 8 seconds'); // Updated timeout message
                // Optionally, retry the request or notify the user
                chrome.runtime.sendMessage({
                    type: 'ERROR',
                    error: 'Connection timeout. Please check your internet connection.'
                });
            } else {
                // Handle other types of errors
                chrome.runtime.sendMessage({
                    type: 'ERROR',
                    error: `Safety check error: ${error.message}`
                });
            }
            // Log detailed error information
            console.error('Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack,
                url: url,
                email: email
            });
        }
    }

    function scheduleEyeReminder(url, tabId) {
        const REMINDER_ALARM_NAME = `eye_reminder_${url}_${tabId}`; // Make alarm name unique per tab

        // Clear any existing alarm for this specific tab and URL first
        chrome.alarms.clear(REMINDER_ALARM_NAME);

        chrome.storage.sync.get(['eyeProtectionInterval'], (settings) => {
            const intervalMinutes = settings.eyeProtectionInterval || 30; // Default to 30 if not set
            console.log(`[Background] Scheduling eye reminder for tab ${tabId} (URL: ${url}) with interval: ${intervalMinutes} minutes. Alarm name: ${REMINDER_ALARM_NAME}`); // Added log

            // Create a repeating alarm using the user-defined interval
            chrome.alarms.create(REMINDER_ALARM_NAME, { periodInMinutes: intervalMinutes });
                console.log(`[Background] Alarm ${REMINDER_ALARM_NAME} created.`); // Added log
        });
        // Note: The onAlarm listener is now handled globally below.
        // Log the scheduling itself? Might be too noisy. Log when it fires instead.
    }

    // Schedule the start of the blocking period
    function scheduleBlockingPeriodStart(triggeringHostname, timerDurationMs, blockingDurationMs) {
        const delayMinutes = Math.max(1, Math.ceil(timerDurationMs / 60000)); // Delay is the timer duration
        const alarmName = `startBlockingPeriod_${triggeringHostname}_${blockingDurationMs}`; // Include durations in name
        chrome.alarms.create(alarmName, { delayInMinutes: delayMinutes });
        console.log(`Scheduled start of blocking period for domain ${triggeringHostname} in ${delayMinutes} minutes. Blocking duration: ${blockingDurationMs / 60000} mins. Alarm: ${alarmName}`);
        sendGA4Event('blocking_period_scheduled', {
            hostname: triggeringHostname,
            delay_minutes: delayMinutes,
            block_duration_ms: blockingDurationMs
        });
    }

    // Function to redirect tabs matching a hostname
    function redirectTabs(hostname) {
        chrome.tabs.query({ url: `*://${hostname}/*` }, (tabs) => {
            tabs.forEach(tab => {
                // Avoid redirecting already closed/internal pages
                if (tab.url && !tab.url.startsWith('chrome-extension://')) {
                    chrome.tabs.update(tab.id, { url: chrome.runtime.getURL('closed.html') })
                        .catch(err => console.log(`Error redirecting tab ${tab.id} for ${hostname}: ${err.message}`));
                }
            });
        });
    }

    // Check page status during active blocking period or normally
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        // Check only when loading is complete and URL is valid http/https
        if (changeInfo.status === 'complete' && tab.url && tab.url.startsWith('http')) {
            chrome.storage.local.get(['blockingPeriodEndTime'], async (result) => {
                const endTime = result.blockingPeriodEndTime;
                const now = Date.now();

                if (endTime && now < endTime) {
                    // --- Currently in Blocking Period ---
                    console.log(`Blocking period active until ${new Date(endTime).toLocaleTimeString()}. Checking new URL: ${tab.url}`);
                    try {
                        // Need to check if this specific page contains games
                        const storageData = await new Promise(resolve => chrome.storage.sync.get(['email'], resolve)); // Need email for check
                        if (!storageData.email) {
                             console.log("No email found, cannot check page during blocking period.");
                             return; // Or handle differently? Maybe block all non-whitelisted? For now, allow.
                        }

                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 5000); // Shorter timeout for this check?

                        const apiUrl = `${SERVER_URL}${CHECK_ENDPOINT}`;
                        let response;
                        try {
                             response = await fetch(apiUrl, {
                                 method: 'POST',
                                 headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                                 body: JSON.stringify({ url: tab.url, email: storageData.email }),
                                 signal: controller.signal
                             });
                        } finally {
                             clearTimeout(timeoutId);
                        }

                        if (response && response.ok) {
                            const data = await response.json();
                            if (data.contains_games === true) {
                                console.log(`Blocking game/entertainment page (${tab.url}) during active blocking period.`);
                                chrome.tabs.update(tabId, { url: chrome.runtime.getURL('closed.html') })
                                    .catch(err => console.log(`Error redirecting tab ${tabId}: ${err.message}`));
                            } else {
                                 console.log(`Allowing non-game page (${tab.url}) during blocking period.`);
                                 // Check if whitelisted before scheduling eye reminder
                                 const urlObj = new URL(tab.url);
                                 const isWhitelisted = combinedWhitelist.some(domain => {
                                     if (domain.startsWith('*')) {
                                         const baseDomain = domain.slice(2);
                                         return urlObj.hostname.endsWith(baseDomain);
                                     }
                                     return urlObj.hostname === domain;
                                 });
                                 if (!isWhitelisted) {
                                     scheduleEyeReminder(tab.url, tabId);
                                 }
                            }
                        } else {
                            console.error(`Backend check failed during blocking period for ${tab.url}. Status: ${response?.status}. Allowing page.`);
                            // Decide fallback behavior: allow or block? Allowing seems safer.
                        }
                    } catch (error) {
                        console.error(`Error checking page during blocking period (${tab.url}):`, error);
                        // Fallback: Allow page load
                    }

                } else {
                    // --- Not in Blocking Period (or expired) ---
                    if (endTime && now >= endTime) {
                        // Blocking period expired, clear the state
                        chrome.storage.local.remove('blockingPeriodEndTime', () => {
                            console.log('Blocking period ended and state cleared.');
                        });
                    }
                    // Standard page load check (handled by the runtime.onMessage listener for PAGE_LOADED)
                    // No action needed here, the message listener will trigger checkPageSafety if required.
                     console.log(`Not in blocking period. URL: ${tab.url}. Standard checks will apply.`);
                }
            });
        }
    });

    // Track active URLs and their time
    let activeUrlTimes = {};
    let activeUrlStart = {};

    // Track tab URL changes
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
        if (changeInfo.status === 'complete' && tab.url) {
            // Ignore internal chrome URLs
            if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                return;
            }
            const now = Date.now();

            // End timing for previous URL in this tab
            if (activeUrlStart[tabId]) {
                const duration = now - activeUrlStart[tabId].start;
                const previousUrl = activeUrlStart[tabId].url;
                // Only track time if the URL is valid http/https
                if (previousUrl.startsWith('http')) {
                    activeUrlTimes[previousUrl] = (activeUrlTimes[previousUrl] || 0) + duration;
                    console.log(`Tracked ${Math.round(duration/1000)}s for ${previousUrl}`);
                }
                delete activeUrlStart[tabId]; // Remove previous entry regardless
            }

            // Start timing for new valid URL
            if (tab.url.startsWith('http')) {
                activeUrlStart[tabId] = {
                    url: tab.url,
                    start: now
                };
                console.log('Started tracking time for:', tab.url);
            }
        }
    });

    // Track when tabs are closed
    chrome.tabs.onRemoved.addListener(tabId => {
        if (activeUrlStart[tabId]) {
            const duration = Date.now() - activeUrlStart[tabId].start;
            const url = activeUrlStart[tabId].url;
            if (url.startsWith('http')) { // Only track valid URLs
                activeUrlTimes[url] = (activeUrlTimes[url] || 0) + duration;
                console.log(`Tracked ${Math.round(duration/1000)}s for ${url} (tab closed)`);
            }
            delete activeUrlStart[tabId];
        }
    });

    // Track when browser window loses/gains focus
    chrome.windows.onFocusChanged.addListener((windowId) => {
        const now = Date.now();
        if (windowId === chrome.windows.WINDOW_ID_NONE) {
            // Browser lost focus - pause all timers
            Object.entries(activeUrlStart).forEach(([tabId, data]) => {
                if (data.url.startsWith('http')) { // Only track valid URLs
                    const duration = now - data.start;
                    activeUrlTimes[data.url] = (activeUrlTimes[data.url] || 0) + duration;
                    console.log(`Paused tracking for ${data.url} after ${Math.round(duration/1000)}s`);
                }
                // Mark as paused by setting start time to 0 or a flag
                data.start = 0; // Using 0 to indicate paused
            });
        } else {
            // Browser gained focus - resume timers for the active tab in the focused window
            chrome.tabs.query({ active: true, windowId: windowId }, (tabs) => {
                if (tabs.length > 0) {
                    const activeTabId = tabs[0].id;
                    if (activeUrlStart[activeTabId] && activeUrlStart[activeTabId].start === 0) { // Check if it was paused
                        activeUrlStart[activeTabId].start = now;
                        console.log(`Resumed tracking for ${activeUrlStart[activeTabId].url}`);
                    }
                }
            });
        }
    });

    async function getBrowsingHistory() {
        console.log('Getting browsing history...');

        // Update duration for currently active URLs before fetching history
        const now = Date.now();
        Object.entries(activeUrlStart).forEach(([tabId, data]) => {
            if (data.start !== 0 && data.url.startsWith('http')) { // Only update if not paused and valid URL
                const duration = now - data.start;
                activeUrlTimes[data.url] = (activeUrlTimes[data.url] || 0) + duration;
                activeUrlStart[tabId].start = now; // Reset start time for continued tracking
            }
        });

        // Get today's history from Chrome's history API
        const startTime = new Date();
        startTime.setHours(0, 0, 0, 0);

        return new Promise((resolve) => {
            chrome.history.search({
                text: '', // Empty text retrieves all history
                startTime: startTime.getTime(),
                maxResults: 1000 // Limit results if necessary
            }, (historyItems) => {
                console.log('Retrieved', historyItems.length, 'history items from Chrome API');

                // Combine Chrome history with tracked time
                const combinedHistory = {};
                historyItems.forEach(item => {
                    if (item.url && item.url.startsWith('http')) { // Process only valid URLs
                        if (!combinedHistory[item.url]) {
                            combinedHistory[item.url] = {
                                url: item.url,
                                title: item.title || 'No Title',
                                visitCount: 0,
                                timeSpent: 0 // Initialize time spent
                            };
                        }
                        combinedHistory[item.url].visitCount += item.visitCount || 1; // Increment visit count
                    }
                });

                // Add tracked time spent
                Object.entries(activeUrlTimes).forEach(([url, timeMs]) => {
                    if (combinedHistory[url]) {
                        combinedHistory[url].timeSpent += Math.round(timeMs / 1000); // Add tracked time in seconds
                    } else if (url.startsWith('http')) {
                        // Add entry if it was tracked but not in history API results (e.g., closed tab)
                        combinedHistory[url] = {
                            url: url,
                            title: 'Title Not Available', // Or try to fetch title if needed
                            visitCount: 1, // Assume at least one visit if time was tracked
                            timeSpent: Math.round(timeMs / 1000)
                        };
                    }
                });

                // Convert back to array format
                const formattedHistory = Object.values(combinedHistory);
                console.log('Formatted history with time spent:', formattedHistory.length, 'entries');
                resolve(formattedHistory);
            });
        });
        // Reset tracked times after fetching history, before sending
        sendGA4Event('browsing_history_compiled', { entry_count: formattedHistory.length });
        activeUrlTimes = {};
    }


    // --- Periodic Status Check ---
    const PERIODIC_CHECK_ALARM_NAME = 'periodicStatusCheck';
    const PERIODIC_CHECK_INTERVAL_MINUTES = 12 * 60; // 12 hours

    async function performPeriodicStatusCheck() {
        console.log(`Performing periodic status check (Alarm: ${PERIODIC_CHECK_ALARM_NAME})...`);
        try {
            // Fetch email from storage first, as /status might not need it but good practice
            const storageData = await new Promise(resolve => chrome.storage.sync.get(['email'], resolve));
            const userEmail = storageData.email;

            // Note: The backend /status endpoint might need the email, or it might use session/token.
            // Assuming GET /status doesn't need email in body based on popup.js usage.
            // If it *does* need email, we'd need to adjust the fetch call (e.g., POST with body or add query param).
            const response = await fetch(`${SERVER_URL}${STATUS_ENDPOINT}`, { // Using GET as per popup.js
                 method: 'GET',
                 mode: 'cors',
                 headers: {
                     'Content-Type': 'application/json',
                     // If authentication is needed (e.g., sending email header):
                     // 'X-User-Email': userEmail || ''
                 }
                 // If POST is needed:
                 // method: 'POST',
                 // body: JSON.stringify({ email: userEmail })
            });

            if (!response.ok) {
                let errorDetails = 'Unknown server error';
                try {
                    const errorData = await response.json();
                    errorDetails = errorData.error || JSON.stringify(errorData);
                } catch (e) {
                    errorDetails = await response.text();
                }
                throw new Error(`Server error: ${response.status} - ${errorDetails}`);
            }

            const data = await response.json();
            console.log('Periodic check - Backend /status response:', data);

            // Update chrome.storage with the status string directly
            const newStatus = data.status; // e.g., 'Free', 'TrialActive', 'Subscribed'
            const newEmail = data.email || null;
            const newTrialUsed = data.trialUsed || false; // Get trialUsed status from backend

            // Get current storage to compare
            const currentStorage = await new Promise(resolve => chrome.storage.sync.get(['userStatus', 'email', 'trialUsed'], resolve));

            if (currentStorage.userStatus !== newStatus || currentStorage.email !== newEmail || currentStorage.trialUsed !== newTrialUsed) {
                const storageUpdate = {
                    userStatus: newStatus,
                    email: newEmail,
                    trialUsed: newTrialUsed // Store trialUsed status
                };
                sendGA4Event('status_updated_periodic', {
                     status: newStatus,
                     email_present: !!newEmail,
                     trial_used: newTrialUsed.toString()
                });
                chrome.storage.sync.set(storageUpdate, () => {
                     console.log('Periodic check - Chrome storage updated:', storageUpdate);
                });
            } else {
                console.log('Periodic check - Status, email, and trialUsed unchanged, storage not updated.');
            }

        } catch (error) {
            console.error('Error during periodic status check:', error);
            // Don't update storage on error, keep existing state
        }
    }

    // --- Setup Alarms on Startup ---
    chrome.runtime.onStartup.addListener(() => {
        console.log("Browser startup detected. Setting up alarms.");
        // Re-create periodic check alarm on browser startup
        chrome.alarms.create(PERIODIC_CHECK_ALARM_NAME, {
            delayInMinutes: 1, // Start check 1 minute after browser starts
            periodInMinutes: PERIODIC_CHECK_INTERVAL_MINUTES
        });
        // Re-setup email history alarm if enabled
        chrome.storage.sync.get(['emailHistoryEnabled', 'emailHistoryTime'], (data) => {
            if (data.emailHistoryEnabled && data.emailHistoryTime) {
                updateEmailHistoryAlarm(true, data.emailHistoryTime);
            }
        });
    });

    // Modify create alarms on install listener
    chrome.runtime.onInstalled.addListener(async (details) => {
        // --- Installation Logging & State Reset ---
        if (details.reason === 'install') {
            console.log('Fresh install detected. Clearing user state...');
            // Define keys to clear (include userStatus)
            const syncKeysToRemove = [
                'userStatus', 'email', 'subscribed', 'inTrial', 'trialExpired', // Clear old and new status keys
                'trialUsed', // Clear trialUsed flag
                'settingsPassword', 'settingsUnlocked',
                'customWhitelist', 'customWhitelistEnabled',
                'emailHistoryEnabled', 'emailHistoryTime',
                'timerMinutes', 'blockingMinutes', 'eyeProtectionInterval'
            ];
            const localKeysToRemove = ['clientId'];

            // Use Promise.all to clear storage concurrently
            try {
                await Promise.all([
                    new Promise(resolve => chrome.storage.sync.remove(syncKeysToRemove, resolve)),
                    new Promise(resolve => chrome.storage.local.remove(localKeysToRemove, resolve))
                ]);
                console.log('Cleared sync and local storage for fresh install.');
            } catch (clearError) {
                console.error('Error clearing storage during install:', clearError);
                // Continue with installation attempt even if clearing fails
            }

            // Now proceed with installation logging and setting initial trial state
            try {
                const response = await fetch(`${SERVER_URL}/log-install`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                if (response.ok) {
                    console.log('Installation logged successfully');
                    sendGA4Event('extension_installed');
                    // Set initial state AFTER clearing - Default to Free
                    chrome.storage.sync.set({ userStatus: STATUS_FREE, email: null, trialUsed: false });
                } else {
                    const errorText = await response.text();
                    console.error('Failed to log installation:', errorText);
                    sendGA4Event('install_log_failed', { error: errorText });
                    // Still set initial state even if logging fails
                    chrome.storage.sync.set({ userStatus: STATUS_FREE, email: null, trialUsed: false });
                }
            } catch (error) {
                console.error('Error logging installation:', error);
                 // Still set initial state even if logging fails
                chrome.storage.sync.set({ userStatus: STATUS_FREE, email: null, trialUsed: false });
            }
        }

        // --- Alarm Setup (Install or Update) ---
        // This part runs for both 'install' and 'update'
         if (details.reason === 'install' || details.reason === 'update') {
            console.log(`Extension ${details.reason} detected. Setting up alarms.`);
            // Create periodic check alarm
            chrome.alarms.create(PERIODIC_CHECK_ALARM_NAME, {
                delayInMinutes: 1, // Start check 1 minute after install/update
                periodInMinutes: PERIODIC_CHECK_INTERVAL_MINUTES
            });
            // Setup email history alarm based on stored settings (might not exist on first install)
            chrome.storage.sync.get(['emailHistoryEnabled', 'emailHistoryTime'], (data) => {
                if (data.emailHistoryEnabled && data.emailHistoryTime) {
                    updateEmailHistoryAlarm(true, data.emailHistoryTime);
                }
            });

            // Set the uninstall URL
            const uninstallUrl = "http://localhost:32410/staticHosting/uninstall_survey.html"; // LOCAL URL FOR TESTING
            chrome.runtime.setUninstallURL(uninstallUrl, () => {
                if (chrome.runtime.lastError) {
                    console.error('Error setting uninstall URL:', chrome.runtime.lastError.message);
                } else {
                    console.log(`Uninstall URL set to: ${uninstallUrl}`);
                }
            });
         }
    });


    // Modify the global alarm listener to handle the new periodic check
    chrome.alarms.onAlarm.addListener((alarm) => {
        console.log('[Background] Global Alarm Fired:', alarm.name);

        // --- Handle Periodic Status Check ---
        if (alarm.name === PERIODIC_CHECK_ALARM_NAME) {
            performPeriodicStatusCheck();
        }
        // --- Handle Eye Protection Reminder Alarms ---
        else if (alarm.name.startsWith('eye_reminder_')) {
            const parts = alarm.name.split('_');
            const tabId = parseInt(parts[parts.length - 1], 10);
            if (!isNaN(tabId)) {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError || !tab) {
                        console.log(`Tab ${tabId} not found for eye reminder, clearing alarm ${alarm.name}`);
                        chrome.alarms.clear(alarm.name);
                    } else {
                        console.log(`Sending eye reminder to tab ${tabId}`);
                        sendGA4Event('eye_reminder_fired', { tab_id: tabId, page_location: tab.url });
                        chrome.tabs.sendMessage(tabId, { action: 'showEyeReminder' }).catch(err => {
                            console.log(`Error sending eye reminder to tab ${tabId}, likely closed or navigated away:`, err);
                             sendGA4Event('eye_reminder_send_failed', { tab_id: tabId, error: err.message });
                            chrome.alarms.clear(alarm.name);
                        });
                    }
                });
            } else {
                console.error(`Could not parse tabId from alarm name: ${alarm.name}`);
                chrome.alarms.clear(alarm.name);
            }
        }
        // --- Handle Daily History Email Alarm ---
        else if (alarm.name === 'dailyHistoryEmail') {
            // Use userStatus for check
            chrome.storage.sync.get(['userStatus', 'emailHistoryEnabled', 'email'], async (data) => {
                const isPremiumFeatureActive = data.userStatus === STATUS_TRIAL_ACTIVE || data.userStatus === STATUS_SUBSCRIBED;

                if (!isPremiumFeatureActive) {
                     console.log(`User status (${data.userStatus}) is not TrialActive or Subscribed, skipping history email.`);
                     return;
                }
                 if (!data.emailHistoryEnabled || !data.email) {
                    console.log('Email history disabled in settings or no email present, skipping history email.');
                    return;
                }
                // Proceed if Premium/Trial, enabled, and email exists
                try {
                    const history = await getBrowsingHistory();
                    const response = await fetch(`${SERVER_URL}/send-history-email`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ email: data.email, history: history })
                    });
                    if (response.ok) {
                        console.log('History email sent successfully');
                        sendGA4Event('email_history_sent', { history_length: history.length }); // Don't send email PII
                        // activeUrlTimes = {}; // Reset now done in getBrowsingHistory
                        // activeUrlStart = {};
                    } else {
                        const errorText = await response.text();
                        console.error('Failed to send history email:', errorText);
                        sendGA4Event('email_history_send_failed', { error: errorText }); // Don't send email PII
                    }
                } catch (error) {
                    console.error('Error processing history email:', error);
                }
            });
        }
        // --- Handle Start of Blocking Period Alarm ---
        else if (alarm.name.startsWith('startBlockingPeriod_')) {
            const parts = alarm.name.split('_');
            const triggeringHostname = parts[1];
            const blockingDurationMs = parseInt(parts[2], 10);

            if (triggeringHostname && !isNaN(blockingDurationMs)) {
                const endTime = Date.now() + blockingDurationMs;
                chrome.storage.local.set({ blockingPeriodEndTime: endTime }, () => {
                    console.log(`Blocking period started for ${blockingDurationMs / 60000} minutes (triggered by ${triggeringHostname}). Ends at: ${new Date(endTime).toLocaleTimeString()}`);
                    sendGA4Event('blocking_period_started', {
                        hostname: triggeringHostname,
                        duration_ms: blockingDurationMs,
                        // end_time_epoch: endTime // Might be too large or not standard, stick to duration
                    });
                    // Redirect any tabs currently open on the triggering domain
                    redirectTabs(triggeringHostname);
                });
            } else {
                 console.error(`Could not parse blocking period alarm: ${alarm.name}`);
            }
        }
        // --- Handle Unknown Alarms ---
        else {
            console.warn(`Unknown alarm triggered: ${alarm.name}`);
        }
    });


    // --- Add Message Listener for Starting Trial ---
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'START_TRIAL') {
            console.log('Received request to start trial...');
            // Call the backend endpoint
            fetch(`${SERVER_URL}/start-trial`, {
                method: 'POST',
                // We rely on determineUserStatus middleware on the backend,
                // which uses IP hash, so no body needed here.
                // If backend needed email explicitly: headers: {'Content-Type': 'application/json'}, body: JSON.stringify({ email: userEmailFromStorage })
            })
            .then(response => {
                // Don't throw error for 4xx status codes - these are expected business logic responses
                // Only throw for 5xx server errors or network issues
                if (!response.ok && response.status >= 500) {
                    throw new Error(`Server error: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('/start-trial response:', data);
                if (data && data.status === STATUS_TRIAL_ACTIVE) {
                    // Update local storage on success
                    chrome.storage.sync.set({ userStatus: STATUS_TRIAL_ACTIVE, trialUsed: true }, () => {
                        console.log('Trial started successfully, storage updated.');
                        sendGA4Event('premium_trial_started');
                        sendResponse({ success: true, status: data.status, remainingMs: data.remainingMs });
                    });
                } else {
                    // Handle errors reported by the backend
                    console.error('Failed to start trial:', data.error || 'Unknown error');
                    sendGA4Event('premium_trial_start_failed', { error: data.error || 'Unknown error' });
                    sendResponse({ success: false, error: data.error || 'Failed to start trial.' });
                }
            })
            .catch(error => {
                console.error('Error calling /start-trial endpoint:', error);
                sendGA4Event('premium_trial_start_failed', { error: error.message });
                sendResponse({ success: false, error: 'Network error or server unavailable. Please try again.' });
            });

            return true; // Indicate async response
        }
        // Keep the listener open for other message types if necessary
        // return true; // Uncomment if other async message types exist in this listener
    });
